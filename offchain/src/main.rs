use solana_vntr_sniper::{
    common::{config::Config, constants::RUN_MSG},
    dex::dex_registry::DEXRegistry,
    engine::monitor,
    core::live_config::LiveTradingSetup,
};
use solana_vntr_sniper::common::config::SwapConfig;
use solana_vntr_sniper::engine::swap::SwapDirection;
use solana_vntr_sniper::engine::swap::SwapInType;
use solana_client::rpc_client::RpcClient;
use std::env;
use std::fs::{self, File};
use std::io::{self, Write};
use std::path::Path;
use clap::{Parser, Subcommand};
use anyhow::{Result, anyhow};

#[derive(Parser)]
#[command(name = "arbitrage-bot")]
#[command(about = "Solana arbitrage bot for cross-DEX trading")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Run preprocessing to fetch trending tokens and discover pools
    Preprocess {
        /// Number of trending tokens to fetch
        #[arg(short, long, default_value_t = 20)]
        limit: usize,
        
        /// Path to save the pool cache
        #[arg(short, long, default_value = "pool_cache.json")]
        cache_path: String,
    },
    /// Run the arbitrage monitor
    Monitor {
        /// Minimum price difference percentage to consider for arbitrage
        #[arg(short, long, default_value_t = 1.5)]
        threshold: f64,

        /// Minimum liquidity in SOL to consider for arbitrage
        #[arg(short, long, default_value_t = 10.0)]
        min_liquidity: f64,

        /// Path to the pool cache file
        #[arg(short, long, default_value = "pool_cache.json")]
        cache_path: String,
    },
    /// Start live trading with full arbitrage execution
    LiveTrade {
        /// Enable dry run mode (no actual transactions)
        #[arg(long)]
        dry_run: bool,

        /// Force start even with warnings
        #[arg(long)]
        force: bool,

        /// Maximum trade amount in SOL
        #[arg(long, default_value_t = 0.05)]
        max_trade_amount: f64,
    },
    /// Load and save pool data for different DEXes
    Pools {
        /// Save to specific DEX directories
        #[arg(short, long, default_value_t = true)]
        save: bool,
    },
}

/// Save pools information to respective directories
async fn save_pool_data() -> Result<()> {
    println!("ARBITRAGE BOT: Saving pool data for different DEXes");
    
    // Create RPC client
    let rpc_url = env::var("RPC_HTTP").unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string());
    let rpc_client = RpcClient::new(&rpc_url);
    
    // DEX directories to save pool data
    let dex_dirs = [
        "pools/meteora_dlmm",
        "pools/meteora_pools",
        "pools/orca",
        "pools/pump_swap",
        "pools/raydium_amm",
        "pools/raydium_clmm",
        "pools/raydium_cpmm",
    ];
    
    // Ensure directories exist
    for dir in &dex_dirs {
        if !Path::new(dir).exists() {
            fs::create_dir_all(dir)?;
            println!("Created directory: {}", dir);
        }
    }
    
    // Get DEX registry
    let dex_registry = DEXRegistry::new();
    
    // Process each DEX
    for dex in dex_registry.get_all_dexes() {
        let dex_name = dex.name.as_str();
        
        // Determine the target directory for this DEX
        let target_dir = match dex_name {
            "meteora_dlmm" => Some("pools/meteora_dlmm"),
            "meteora_pools" => Some("pools/meteora_pools"),
            "orca" => Some("pools/orca"),
            "pumpswap" => Some("pools/pump_swap"),
            "raydium_amm" => Some("pools/raydium_amm"),
            "raydium_clmm" => Some("pools/raydium_clmm"),
            "raydium_cpmm" => Some("pools/raydium_cpmm"),
            _ => None,
        };
        
        // Skip DEXes without a target directory
        if target_dir.is_none() {
            continue;
        }
        
        let target_dir = target_dir.unwrap();
        println!("Processing {}...", dex_name);
        
        // Try to fetch pool accounts for this DEX
        // This fetches all accounts owned by the DEX program
        match rpc_client.get_program_accounts(&dex.program_id) {
            Ok(accounts) => {
                println!("Found {} accounts for {}", accounts.len(), dex_name);
                
                // Process each account - we'll only save a minimal template for now
                for (pubkey, _account) in accounts.iter().take(20) { // Limit to prevent overload
                    let pool_id = pubkey.to_string();
                    let file_name = format!("{}_{}.json", pool_id, dex_name);
                    let file_path = format!("{}/{}", target_dir, file_name);
                    
                    // Create an empty JSON file for now - you can expand this
                    // to include parsed pool data in the future
                    let pool_data = serde_json::json!({
                        "pool_id": pool_id,
                        "dex": dex_name,
                        "program_id": dex.program_id.to_string()
                    });
                    
                    // Save the file
                    let mut file = File::create(&file_path)?;
                    file.write_all(serde_json::to_string_pretty(&pool_data)?.as_bytes())?;
                    
                    println!("Saved pool data: {}", file_path);
                }
            },
            Err(e) => {
                println!("Error fetching accounts for {}: {}", dex_name, e);
            }
        }
    }
    
    println!("Pool data saved successfully!");
    Ok(())
}

#[tokio::main]
async fn main() {
    let cli = Cli::parse();
    
    /* Initial Settings */
    let config = Config::new().await;
    let config = config.lock().await;

    match cli.command {
        Commands::Preprocess { limit, cache_path } => {
            println!("ARBITRAGE BOT: Preprocessing - Fetching trending tokens and discovering pools");
            
            // Create RPC client for preprocessing
            let rpc_url = env::var("RPC_HTTP").unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string());
            let rpc_client = RpcClient::new(&rpc_url);
            
            // Run preprocessing (placeholder - implement if needed)
            println!("Preprocessing step - implement if needed for limit: {}, cache_path: {}", limit, cache_path);
        },
        Commands::Monitor { threshold, min_liquidity, cache_path } => {
            println!("ARBITRAGE BOT: Monitoring token prices across multiple DEXes");
            
            /* Display supported DEXes */
            let dex_registry = DEXRegistry::new();
            println!("Tracking DEXes:");
            for dex in dex_registry.get_all_dexes() {
                println!("  - {} ({})", dex.name, dex.program_id);
            }
            
            /* Setup swap config for arbitrage */
            let swap_config = SwapConfig {
                swap_direction: SwapDirection::Buy,
                in_type: SwapInType::Qty,
                amount_in: 0.1, // Default to 0.1 SOL per trade
                slippage: 50, // 0.5% slippage
                use_jito: false, // Don't use Jito MEV protection by default
            };
            
            // Convert min_liquidity from SOL to lamports
            let min_liquidity_lamports = (min_liquidity * 1_000_000_000.0) as u64;

            match monitor::arbitrage_monitor(
                config.yellowstone_grpc_http.clone(),
                config.yellowstone_grpc_token.clone(),
                config.app_state.clone(),
                swap_config,
                60000, // time_exceed: 60 seconds default
                100,   // counter_limit: default
                min_liquidity_lamports, // min_dev_buy
                min_liquidity_lamports * 10, // max_dev_buy (10x min)
            ).await {
                Ok(_) => println!("Arbitrage monitor completed successfully"),
                Err(e) => eprintln!("Arbitrage monitor error: {}", e),
            }
        },
        Commands::Pools { save } => {
            if save {
                match save_pool_data().await {
                    Ok(_) => println!("Pool data saved successfully"),
                    Err(e) => eprintln!("Error saving pool data: {}", e),
                }
            } else {
                println!("Pool data loading is pending implementation");
            }
        },
        Commands::LiveTrade { dry_run, force, max_trade_amount } => {
            println!("🚀 STARTING LIVE ARBITRAGE TRADING");
            println!("{}", "═".repeat(60));

            // Override environment variables if command line options are provided
            if dry_run {
                std::env::set_var("DRY_RUN", "true");
                println!("🟡 DRY RUN MODE ENABLED - No actual transactions will be executed");
            }

            if max_trade_amount != 0.05 {
                std::env::set_var("DEFAULT_TRADE_AMOUNT", &max_trade_amount.to_string());
                println!("💰 Max trade amount set to: {} SOL", max_trade_amount);
            }

            // Setup and validate live trading configuration
            match LiveTradingSetup::setup_and_validate().await {
                Ok(live_config) => {
                    if !force && !dry_run {
                        println!("\n⚠️  WARNING: You are about to start LIVE TRADING with real funds!");
                        println!("Press Ctrl+C to cancel, or any key to continue...");

                        // Wait for user confirmation
                        let mut input = String::new();
                        std::io::stdin().read_line(&mut input).expect("Failed to read input");
                    }

                    println!("\n🎯 Starting arbitrage execution engine...");

                    // Start the live trading monitor with enhanced configuration
                    let swap_config = SwapConfig {
                        swap_direction: SwapDirection::Buy,
                        in_type: SwapInType::Qty,
                        amount_in: live_config.default_trade_amount,
                        slippage: live_config.max_slippage_bps,
                        use_jito: live_config.use_jito,
                    };

                    match monitor::arbitrage_monitor(
                        config.yellowstone_grpc_http.clone(),
                        config.yellowstone_grpc_token.clone(),
                        config.app_state.clone(),
                        swap_config,
                        live_config.max_position_hold_time_ms,
                        100, // counter_limit
                        (live_config.default_trade_amount * 1_000_000_000.0) as u64, // min_dev_buy in lamports
                        (live_config.max_total_position_size_sol * 1_000_000_000.0) as u64, // max_dev_buy in lamports
                    ).await {
                        Ok(_) => println!("✅ Live trading session completed successfully"),
                        Err(e) => eprintln!("❌ Live trading error: {}", e),
                    }
                },
                Err(e) => {
                    eprintln!("❌ Configuration validation failed: {}", e);
                    eprintln!("Please fix the configuration issues before starting live trading.");
                    std::process::exit(1);
                }
            }
        }
    }
}
