//! Live Trading Configuration
//! 
//! This module provides configuration management specifically for live trading,
//! including safety checks, parameter validation, and environment setup.

use std::env;
use anyhow::{Result, anyhow};
use colored::Colorize;
use anchor_client::solana_sdk::signature::Keypair;
use solana_client::nonblocking::rpc_client::RpcClient;
use serde::{Deserialize, Serialize};

use crate::core::config_validator::ConfigValidator;

/// Live trading configuration with safety parameters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiveTradingConfig {
    /// Trading parameters
    pub default_trade_amount: f64,
    pub max_slippage_bps: u64,
    pub min_profit_threshold_bps: u64,
    pub max_position_hold_time_ms: u64,
    pub max_total_position_size_sol: f64,
    
    /// Safety parameters
    pub emergency_stop: bool,
    pub enable_emergency_liquidation: bool,
    pub test_mode: bool,
    pub dry_run: bool,
    
    /// Performance parameters
    pub max_concurrent_swaps: usize,
    pub max_concurrent_rpc_calls: usize,
    pub execution_timeout_seconds: u64,
    
    /// DEX configuration
    pub enable_raydium: bool,
    pub enable_orca: bool,
    pub enable_meteora: bool,
    pub enable_pump_swap: bool,
    
    /// MEV protection
    pub use_jito: bool,
    pub jito_tip_amount: u64,
}

impl Default for LiveTradingConfig {
    fn default() -> Self {
        Self {
            default_trade_amount: 0.05,
            max_slippage_bps: 100, // 1%
            min_profit_threshold_bps: 75, // 0.75%
            max_position_hold_time_ms: 300_000, // 5 minutes
            max_total_position_size_sol: 1.0,
            
            emergency_stop: false,
            enable_emergency_liquidation: true,
            test_mode: false,
            dry_run: false,
            
            max_concurrent_swaps: 3,
            max_concurrent_rpc_calls: 15,
            execution_timeout_seconds: 30,
            
            enable_raydium: true,
            enable_orca: true,
            enable_meteora: true,
            enable_pump_swap: true,
            
            use_jito: false,
            jito_tip_amount: 1000,
        }
    }
}

impl LiveTradingConfig {
    /// Load configuration from environment variables
    pub fn from_env() -> Result<Self> {
        let mut config = Self::default();
        
        // Trading parameters
        if let Ok(val) = env::var("DEFAULT_TRADE_AMOUNT") {
            config.default_trade_amount = val.parse()
                .map_err(|_| anyhow!("Invalid DEFAULT_TRADE_AMOUNT"))?;
        }
        
        if let Ok(val) = env::var("MAX_SLIPPAGE_BPS") {
            config.max_slippage_bps = val.parse()
                .map_err(|_| anyhow!("Invalid MAX_SLIPPAGE_BPS"))?;
        }
        
        if let Ok(val) = env::var("MIN_PROFIT_THRESHOLD_BPS") {
            config.min_profit_threshold_bps = val.parse()
                .map_err(|_| anyhow!("Invalid MIN_PROFIT_THRESHOLD_BPS"))?;
        }
        
        if let Ok(val) = env::var("MAX_POSITION_HOLD_TIME_MS") {
            config.max_position_hold_time_ms = val.parse()
                .map_err(|_| anyhow!("Invalid MAX_POSITION_HOLD_TIME_MS"))?;
        }
        
        if let Ok(val) = env::var("MAX_TOTAL_POSITION_SIZE_SOL") {
            config.max_total_position_size_sol = val.parse()
                .map_err(|_| anyhow!("Invalid MAX_TOTAL_POSITION_SIZE_SOL"))?;
        }
        
        // Safety parameters
        if let Ok(val) = env::var("EMERGENCY_STOP") {
            config.emergency_stop = val.parse()
                .map_err(|_| anyhow!("Invalid EMERGENCY_STOP"))?;
        }
        
        if let Ok(val) = env::var("ENABLE_EMERGENCY_LIQUIDATION") {
            config.enable_emergency_liquidation = val.parse()
                .map_err(|_| anyhow!("Invalid ENABLE_EMERGENCY_LIQUIDATION"))?;
        }
        
        if let Ok(val) = env::var("TEST_MODE") {
            config.test_mode = val.parse()
                .map_err(|_| anyhow!("Invalid TEST_MODE"))?;
        }
        
        if let Ok(val) = env::var("DRY_RUN") {
            config.dry_run = val.parse()
                .map_err(|_| anyhow!("Invalid DRY_RUN"))?;
        }
        
        // Performance parameters
        if let Ok(val) = env::var("MAX_CONCURRENT_SWAPS") {
            config.max_concurrent_swaps = val.parse()
                .map_err(|_| anyhow!("Invalid MAX_CONCURRENT_SWAPS"))?;
        }
        
        if let Ok(val) = env::var("MAX_CONCURRENT_RPC_CALLS") {
            config.max_concurrent_rpc_calls = val.parse()
                .map_err(|_| anyhow!("Invalid MAX_CONCURRENT_RPC_CALLS"))?;
        }
        
        // DEX configuration
        if let Ok(val) = env::var("ENABLE_RAYDIUM") {
            config.enable_raydium = val.parse()
                .map_err(|_| anyhow!("Invalid ENABLE_RAYDIUM"))?;
        }
        
        if let Ok(val) = env::var("ENABLE_ORCA") {
            config.enable_orca = val.parse()
                .map_err(|_| anyhow!("Invalid ENABLE_ORCA"))?;
        }
        
        if let Ok(val) = env::var("ENABLE_METEORA") {
            config.enable_meteora = val.parse()
                .map_err(|_| anyhow!("Invalid ENABLE_METEORA"))?;
        }
        
        if let Ok(val) = env::var("ENABLE_PUMP_SWAP") {
            config.enable_pump_swap = val.parse()
                .map_err(|_| anyhow!("Invalid ENABLE_PUMP_SWAP"))?;
        }
        
        // MEV protection
        if let Ok(val) = env::var("USE_JITO") {
            config.use_jito = val.parse()
                .map_err(|_| anyhow!("Invalid USE_JITO"))?;
        }
        
        if let Ok(val) = env::var("JITO_TIP_AMOUNT") {
            config.jito_tip_amount = val.parse()
                .map_err(|_| anyhow!("Invalid JITO_TIP_AMOUNT"))?;
        }
        
        Ok(config)
    }
    
    /// Validate the configuration for live trading
    pub async fn validate(&self) -> Result<()> {
        println!("🔍 Validating live trading configuration...");
        
        // Check emergency stop
        if self.emergency_stop {
            return Err(anyhow!("❌ EMERGENCY_STOP is enabled - live trading is disabled"));
        }
        
        // Validate trading parameters
        if self.default_trade_amount <= 0.0 {
            return Err(anyhow!("DEFAULT_TRADE_AMOUNT must be greater than 0"));
        }
        
        if self.default_trade_amount > 10.0 {
            return Err(anyhow!("DEFAULT_TRADE_AMOUNT is too high (max 10 SOL for safety)"));
        }
        
        if self.max_slippage_bps > 2000 { // 20%
            return Err(anyhow!("MAX_SLIPPAGE_BPS is too high (max 2000bps for safety)"));
        }
        
        if self.min_profit_threshold_bps < 10 { // 0.1%
            println!("⚠️  WARNING: MIN_PROFIT_THRESHOLD_BPS is very low ({}bps)", self.min_profit_threshold_bps);
        }
        
        if self.max_concurrent_swaps > 10 {
            return Err(anyhow!("MAX_CONCURRENT_SWAPS is too high (max 10 for safety)"));
        }
        
        // Check that at least one DEX is enabled
        if !self.enable_raydium && !self.enable_orca && !self.enable_meteora && !self.enable_pump_swap {
            return Err(anyhow!("At least one DEX must be enabled"));
        }
        
        println!("  ✅ Configuration validated successfully");
        
        Ok(())
    }
    
    /// Display configuration summary
    pub fn display_summary(&self) {
        println!("\n{}", "📊 LIVE TRADING CONFIGURATION SUMMARY".blue().bold());
        println!("{}", "═".repeat(50).blue());
        
        println!("💰 Trading Parameters:");
        println!("  • Default trade amount: {} SOL", self.default_trade_amount);
        println!("  • Max slippage: {}bps ({}%)", self.max_slippage_bps, self.max_slippage_bps as f64 / 100.0);
        println!("  • Min profit threshold: {}bps ({}%)", self.min_profit_threshold_bps, self.min_profit_threshold_bps as f64 / 100.0);
        println!("  • Max position hold time: {}ms", self.max_position_hold_time_ms);
        println!("  • Max total position size: {} SOL", self.max_total_position_size_sol);
        
        println!("\n🛡️  Safety Parameters:");
        println!("  • Emergency stop: {}", if self.emergency_stop { "🔴 ENABLED".red() } else { "🟢 DISABLED".green() });
        println!("  • Emergency liquidation: {}", if self.enable_emergency_liquidation { "🟢 ENABLED".green() } else { "🔴 DISABLED".red() });
        println!("  • Test mode: {}", if self.test_mode { "🟡 ENABLED".yellow() } else { "🟢 DISABLED".green() });
        println!("  • Dry run: {}", if self.dry_run { "🟡 ENABLED".yellow() } else { "🟢 DISABLED".green() });
        
        println!("\n⚡ Performance Parameters:");
        println!("  • Max concurrent swaps: {}", self.max_concurrent_swaps);
        println!("  • Max concurrent RPC calls: {}", self.max_concurrent_rpc_calls);
        println!("  • Execution timeout: {}s", self.execution_timeout_seconds);
        
        println!("\n🏪 DEX Configuration:");
        println!("  • Raydium: {}", if self.enable_raydium { "✅" } else { "❌" });
        println!("  • Orca: {}", if self.enable_orca { "✅" } else { "❌" });
        println!("  • Meteora: {}", if self.enable_meteora { "✅" } else { "❌" });
        println!("  • PumpSwap: {}", if self.enable_pump_swap { "✅" } else { "❌" });
        
        println!("\n🛡️  MEV Protection:");
        println!("  • Use Jito: {}", if self.use_jito { "✅" } else { "❌" });
        if self.use_jito {
            println!("  • Jito tip amount: {} lamports", self.jito_tip_amount);
        }
        
        println!("{}", "═".repeat(50).blue());
    }
}

/// Live trading setup and validation
pub struct LiveTradingSetup;

impl LiveTradingSetup {
    /// Complete setup and validation for live trading
    pub async fn setup_and_validate() -> Result<LiveTradingConfig> {
        println!("{}", "🚀 SETTING UP LIVE TRADING ENVIRONMENT".green().bold());
        println!("{}", "═".repeat(60).green());
        
        // Step 1: Validate basic configuration
        ConfigValidator::validate_all()?;
        
        // Step 2: Validate live trading specific configuration
        ConfigValidator::validate_live_trading_config().await?;
        
        // Step 3: Check wallet balance
        ConfigValidator::validate_wallet_balance().await?;
        
        // Step 4: Load and validate live trading configuration
        let config = LiveTradingConfig::from_env()?;
        config.validate().await?;
        
        // Step 5: Display configuration summary
        config.display_summary();
        
        println!("\n{}", "✅ LIVE TRADING SETUP COMPLETE".green().bold());
        println!("{}", "═".repeat(60).green());
        
        Ok(config)
    }
}
