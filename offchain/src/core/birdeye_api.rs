use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use reqwest::Client;
use anchor_client::solana_sdk::pubkey::Pubkey;
use std::{env, str::FromStr, collections::HashMap};

/// Birdeye API client for fetching token data and prices
pub struct BirdeyeClient {
    client: Client,
    api_key: String,
    base_url: String,
}

/// Token price data from Birdeye API
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct BirdeyeTokenPrice {
    pub address: String,
    pub price: f64,
    #[serde(rename = "priceChange24h")]
    pub price_change_24h: Option<f64>,
    pub volume24h: Option<f64>,
    pub liquidity: Option<f64>,
    pub timestamp: i64,
}

/// Token metadata from Birdeye API
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct BirdeyeTokenMetadata {
    pub address: String,
    pub name: String,
    pub symbol: String,
    pub decimals: u8,
    #[serde(rename = "logoURI")]
    pub logo_uri: Option<String>,
    pub volume24h: Option<f64>,
    pub liquidity: Option<f64>,
    #[serde(rename = "priceChange24h")]
    pub price_change_24h: Option<f64>,
}

/// Trending tokens response from Birdeye API
#[derive(Debug, Deserialize)]
pub struct BirdeyeTrendingResponse {
    pub data: Vec<BirdeyeTokenMetadata>,
    pub success: bool,
}

/// Token price response from Birdeye API
#[derive(Debug, Deserialize)]
pub struct BirdeyePriceResponse {
    pub data: BirdeyeTokenPrice,
    pub success: bool,
}

/// Multi-token price response from Birdeye API
#[derive(Debug, Deserialize)]
pub struct BirdeyeMultiPriceResponse {
    pub data: HashMap<String, BirdeyeTokenPrice>,
    pub success: bool,
}

impl BirdeyeClient {
    /// Create a new Birdeye API client
    pub fn new() -> Result<Self> {
        let api_key = env::var("BIRDEYE_API_KEY")
            .map_err(|_| anyhow!("BIRDEYE_API_KEY environment variable not set"))?;
        
        let client = Client::new();
        let base_url = "https://public-api.birdeye.so".to_string();
        
        Ok(Self {
            client,
            api_key,
            base_url,
        })
    }
    
    /// Create a new client with custom API key
    pub fn with_api_key(api_key: String) -> Self {
        let client = Client::new();
        let base_url = "https://public-api.birdeye.so".to_string();
        
        Self {
            client,
            api_key,
            base_url,
        }
    }
    
    /// Fetch trending tokens from Birdeye API
    pub async fn fetch_trending_tokens(&self, limit: Option<usize>) -> Result<Vec<BirdeyeTokenMetadata>> {
        let url = format!("{}/defi/tokenlist", self.base_url);
        
        println!("Fetching trending tokens from Birdeye API...");
        
        let response = self.client
            .get(&url)
            .header("X-API-KEY", &self.api_key)
            .query(&[("sort_by", "v24hUSD"), ("sort_type", "desc")])
            .send()
            .await
            .map_err(|e| anyhow!("Failed to fetch trending tokens: {}", e))?;
        
        if !response.status().is_success() {
            return Err(anyhow!(
                "Failed to fetch trending tokens: HTTP {}",
                response.status()
            ));
        }
        
        let trending_response: BirdeyeTrendingResponse = response
            .json()
            .await
            .map_err(|e| anyhow!("Failed to parse trending tokens response: {}", e))?;
        
        if !trending_response.success {
            return Err(anyhow!("Birdeye API returned success=false"));
        }
        
        let mut tokens = trending_response.data;
        
        // Validate that addresses are valid Solana pubkeys
        tokens.retain(|token| Pubkey::from_str(&token.address).is_ok());
        
        // Apply limit if specified
        if let Some(limit) = limit {
            if tokens.len() > limit {
                tokens.truncate(limit);
            }
        }
        
        println!("Found {} valid trending tokens from Birdeye", tokens.len());
        
        Ok(tokens)
    }
    
    /// Get price for a single token
    pub async fn get_token_price(&self, token_address: &str) -> Result<BirdeyeTokenPrice> {
        let url = format!("{}/defi/price", self.base_url);
        
        let response = self.client
            .get(&url)
            .header("X-API-KEY", &self.api_key)
            .query(&[("address", token_address)])
            .send()
            .await
            .map_err(|e| anyhow!("Failed to fetch token price: {}", e))?;
        
        if !response.status().is_success() {
            return Err(anyhow!(
                "Failed to fetch token price: HTTP {}",
                response.status()
            ));
        }
        
        let price_response: BirdeyePriceResponse = response
            .json()
            .await
            .map_err(|e| anyhow!("Failed to parse price response: {}", e))?;
        
        if !price_response.success {
            return Err(anyhow!("Birdeye API returned success=false for price"));
        }
        
        Ok(price_response.data)
    }
    
    /// Get prices for multiple tokens
    pub async fn get_multiple_token_prices(&self, token_addresses: &[String]) -> Result<HashMap<String, BirdeyeTokenPrice>> {
        let url = format!("{}/defi/multi_price", self.base_url);
        
        // Birdeye API accepts comma-separated addresses
        let addresses_param = token_addresses.join(",");
        
        let response = self.client
            .get(&url)
            .header("X-API-KEY", &self.api_key)
            .query(&[("list_address", &addresses_param)])
            .send()
            .await
            .map_err(|e| anyhow!("Failed to fetch multiple token prices: {}", e))?;
        
        if !response.status().is_success() {
            return Err(anyhow!(
                "Failed to fetch multiple token prices: HTTP {}",
                response.status()
            ));
        }
        
        let price_response: BirdeyeMultiPriceResponse = response
            .json()
            .await
            .map_err(|e| anyhow!("Failed to parse multi-price response: {}", e))?;
        
        if !price_response.success {
            return Err(anyhow!("Birdeye API returned success=false for multi-price"));
        }
        
        Ok(price_response.data)
    }
    
    /// Get token overview including price, volume, and liquidity
    pub async fn get_token_overview(&self, token_address: &str) -> Result<BirdeyeTokenMetadata> {
        let url = format!("{}/defi/token_overview", self.base_url);
        
        let response = self.client
            .get(&url)
            .header("X-API-KEY", &self.api_key)
            .query(&[("address", token_address)])
            .send()
            .await
            .map_err(|e| anyhow!("Failed to fetch token overview: {}", e))?;
        
        if !response.status().is_success() {
            return Err(anyhow!(
                "Failed to fetch token overview: HTTP {}",
                response.status()
            ));
        }
        
        let overview: BirdeyeTokenMetadata = response
            .json()
            .await
            .map_err(|e| anyhow!("Failed to parse token overview response: {}", e))?;
        
        Ok(overview)
    }
    
    /// Get SOL price in USD (useful for calculations)
    pub async fn get_sol_price_usd(&self) -> Result<f64> {
        const SOL_MINT: &str = "So11111111111111111111111111111111111111112";
        
        let price_data = self.get_token_price(SOL_MINT).await?;
        Ok(price_data.price)
    }
}

/// Convert Birdeye token metadata to our internal TokenMetadata format
impl From<BirdeyeTokenMetadata> for crate::core::jupiter_api::TokenMetadata {
    fn from(birdeye_token: BirdeyeTokenMetadata) -> Self {
        Self {
            mint: birdeye_token.address,
            name: birdeye_token.name,
            symbol: birdeye_token.symbol,
            decimals: birdeye_token.decimals,
            logo_uri: birdeye_token.logo_uri,
            daily_volume: birdeye_token.volume24h,
        }
    }
}

/// Utility function to get trending tokens using Birdeye API
pub async fn fetch_trending_tokens_birdeye(limit: usize) -> Result<Vec<crate::core::jupiter_api::TokenMetadata>> {
    let client = BirdeyeClient::new()?;
    let birdeye_tokens = client.fetch_trending_tokens(Some(limit)).await?;
    
    let tokens = birdeye_tokens
        .into_iter()
        .map(|token| token.into())
        .collect();
    
    Ok(tokens)
}

/// Utility function to get token price using Birdeye API
pub async fn get_token_price_birdeye(token_address: &str) -> Result<f64> {
    let client = BirdeyeClient::new()?;
    let price_data = client.get_token_price(token_address).await?;
    Ok(price_data.price)
}
