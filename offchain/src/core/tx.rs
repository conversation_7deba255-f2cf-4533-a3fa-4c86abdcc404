use std::sync::Arc;
use anyhow::{Result, anyhow};
use anchor_client::solana_sdk::{
    instruction::Instruction,
    pubkey::Pubkey,
    signature::{Signature, Keypair},
    transaction::Transaction,
    hash::Hash,
    compute_budget::ComputeBudgetInstruction,
};
use serde::{Deserialize, Serialize};

use crate::core::app_state::EnhancedAppState;

/// Enhanced transaction builder with atomic execution support
pub struct TransactionBuilder {
    app_state: Arc<EnhancedAppState>,
    instructions: Vec<Instruction>,
    payer: Option<Pubkey>,
    compute_unit_limit: Option<u32>,
    compute_unit_price: Option<u64>,
    enable_simulation: bool,
}

/// Transaction execution result
#[derive(Debug, Clone)]
pub struct TransactionResult {
    pub signature: String,
    pub success: bool,
    pub compute_units_consumed: Option<u64>,
    pub error_message: Option<String>,
}

/// Transaction simulation result
#[derive(Debug, Clone)]
pub struct SimulationResult {
    pub success: bool,
    pub compute_units_consumed: Option<u64>,
    pub error_message: Option<String>,
    pub logs: Vec<String>,
}

impl TransactionBuilder {
    /// Create a new transaction builder
    pub fn new(app_state: Arc<EnhancedAppState>) -> Self {
        Self {
            app_state,
            instructions: Vec::new(),
            payer: None,
            compute_unit_limit: None,
            compute_unit_price: None,
            enable_simulation: true,
        }
    }

    /// Add an instruction to the transaction
    pub fn add_instruction(&mut self, instruction: Instruction) -> &mut Self {
        self.instructions.push(instruction);
        self
    }

    /// Add multiple instructions to the transaction
    pub fn add_instructions(&mut self, instructions: Vec<Instruction>) -> &mut Self {
        self.instructions.extend(instructions);
        self
    }

    /// Set the transaction payer
    pub fn set_payer(&mut self, payer: Pubkey) -> &mut Self {
        self.payer = Some(payer);
        self
    }

    /// Set compute unit limit
    pub fn set_compute_unit_limit(&mut self, limit: u32) -> &mut Self {
        self.compute_unit_limit = Some(limit);
        self
    }

    /// Set compute unit price (priority fee)
    pub fn set_compute_unit_price(&mut self, price: u64) -> &mut Self {
        self.compute_unit_price = Some(price);
        self
    }

    /// Enable or disable transaction simulation
    pub fn enable_simulation(&mut self, enable: bool) -> &mut Self {
        self.enable_simulation = enable;
        self
    }

    /// Build the transaction with all instructions
    pub async fn build(&self) -> Result<Transaction> {
        let payer = self.payer.unwrap_or_else(|| self.app_state.keypair.pubkey());

        let mut final_instructions = Vec::new();

        // Add compute budget instructions if specified
        if let Some(limit) = self.compute_unit_limit {
            final_instructions.push(ComputeBudgetInstruction::set_compute_unit_limit(limit));
        }

        if let Some(price) = self.compute_unit_price {
            final_instructions.push(ComputeBudgetInstruction::set_compute_unit_price(price));
        }

        // Add user instructions
        final_instructions.extend(self.instructions.clone());

        // Get recent blockhash
        let rpc_client = self.app_state.get_rpc_client().await?;
        let recent_blockhash = rpc_client.get_latest_blockhash().await?;

        let mut transaction = Transaction::new_with_payer(&final_instructions, Some(&payer));
        transaction.sign(&[&*self.app_state.keypair], recent_blockhash);

        Ok(transaction)
    }

    /// Build and send transaction
    pub async fn build_and_send_transaction(&self, instructions: Vec<Instruction>) -> Result<TransactionResult> {
        let mut builder = TransactionBuilder::new(self.app_state.clone());
        builder.add_instructions(instructions);

        if let Some(limit) = self.compute_unit_limit {
            builder.set_compute_unit_limit(limit);
        }

        if let Some(price) = self.compute_unit_price {
            builder.set_compute_unit_price(price);
        }

        let transaction = builder.build().await?;

        // Simulate if enabled
        if self.enable_simulation {
            let simulation = self.simulate_transaction_internal(&transaction).await?;
            if !simulation.success {
                return Ok(TransactionResult {
                    signature: String::new(),
                    success: false,
                    compute_units_consumed: simulation.compute_units_consumed,
                    error_message: simulation.error_message,
                });
            }
        }

        // Send transaction
        let rpc_client = self.app_state.get_rpc_client().await?;
        let signature = rpc_client.send_transaction(&transaction).await?;

        // Wait for confirmation
        let confirmation = rpc_client.confirm_transaction(&signature).await?;

        Ok(TransactionResult {
            signature: signature.to_string(),
            success: confirmation,
            compute_units_consumed: None, // Would need to parse transaction response for this
            error_message: if confirmation { None } else { Some("Transaction failed to confirm".to_string()) },
        })
    }

    /// Simulate transaction execution
    pub async fn simulate_transaction(&self, instructions: Vec<Instruction>) -> Result<SimulationResult> {
        let mut builder = TransactionBuilder::new(self.app_state.clone());
        builder.add_instructions(instructions);

        if let Some(limit) = self.compute_unit_limit {
            builder.set_compute_unit_limit(limit);
        }

        if let Some(price) = self.compute_unit_price {
            builder.set_compute_unit_price(price);
        }

        let transaction = builder.build().await?;
        self.simulate_transaction_internal(&transaction).await
    }

    /// Internal simulation method
    async fn simulate_transaction_internal(&self, transaction: &Transaction) -> Result<SimulationResult> {
        // This would use the actual RPC simulate_transaction method
        // For now, return a mock successful simulation
        Ok(SimulationResult {
            success: true,
            compute_units_consumed: Some(100_000),
            error_message: None,
            logs: vec!["Program log: Simulation successful".to_string()],
        })
    }

    /// Create atomic transaction bundle
    pub async fn create_atomic_bundle(&self, instruction_groups: Vec<Vec<Instruction>>) -> Result<Vec<Transaction>> {
        let mut transactions = Vec::new();

        for instructions in instruction_groups {
            let mut builder = TransactionBuilder::new(self.app_state.clone());
            builder.add_instructions(instructions);

            if let Some(limit) = self.compute_unit_limit {
                builder.set_compute_unit_limit(limit);
            }

            if let Some(price) = self.compute_unit_price {
                builder.set_compute_unit_price(price);
            }

            let transaction = builder.build().await?;
            transactions.push(transaction);
        }

        Ok(transactions)
    }

    /// Clear all instructions
    pub fn clear(&mut self) -> &mut Self {
        self.instructions.clear();
        self
    }

    /// Get instruction count
    pub fn instruction_count(&self) -> usize {
        self.instructions.len()
    }
}

// Note: Default implementation removed as TransactionBuilder now requires app_state parameter

pub async fn new_signed_and_send_zeroslot(
    transaction: Transaction,
    rpc_client: &anchor_client::solana_client::rpc_client::RpcClient,
) -> Result<Signature> {
    // TODO: Implement transaction signing and sending
    let signature = rpc_client.send_transaction(&transaction)?;
    Ok(signature)
}
