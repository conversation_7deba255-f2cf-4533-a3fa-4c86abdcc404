//! Enhanced Arbitrage Execution Engine
//!
//! This module handles the execution of arbitrage opportunities with flash loan integration,
//! atomic swap execution, MEV protection, and comprehensive transaction management.

use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use anyhow::{Result, anyhow};
use solana_sdk::{
    pubkey::Pubkey,
    signature::{Keypair, Signature},
    signer::Signer,
    transaction::Transaction,
    instruction::Instruction,
};
use solana_client::nonblocking::rpc_client::RpcClient;
use colored::Colorize;

use crate::core::{
    priority_queue::{OpportunityQueue, PrioritizedOpportunity, ArbitrageStrategy, ArbitrageOpportunity},
    rate_limiter::RateLimiter,
    circuit_breaker::CircuitBreaker,
    retry::{retry_with_backoff, RetryConfig},
    app_state::EnhancedAppState,
    flash_loan::{FlashLoanManager, FlashLoanRequest, FlashLoanConfig},
    atomic_executor::{AtomicExecutor, AtomicSwapRequest, SwapStep, AtomicExecutorConfig},
    mev_protection::{MevProtectionManager, MevProtectionConfig, SubmissionStrategy},
    tx::TransactionBuilder,
};
use crate::common::config::SwapConfig;

/// Enhanced configuration for the execution engine
#[derive(Clone, Debug)]
pub struct ExecutionEngineConfig {
    pub max_concurrent_executions: usize,
    pub execution_timeout_seconds: u64,
    pub max_slippage_bps: u64,
    pub enable_simulation: bool,
    pub enable_bundling: bool,
    pub priority_fee_lamports: u64,
    pub enable_flash_loans: bool,
    pub enable_mev_protection: bool,
    pub min_profit_threshold_lamports: u64,
    pub max_flash_loan_amount_sol: f64,
    pub flash_loan_config: FlashLoanConfig,
    pub atomic_executor_config: AtomicExecutorConfig,
    pub mev_protection_config: MevProtectionConfig,
}

impl Default for ExecutionEngineConfig {
    fn default() -> Self {
        Self {
            max_concurrent_executions: 3,
            execution_timeout_seconds: 30,
            max_slippage_bps: 100, // 1%
            enable_simulation: true,
            enable_bundling: false,
            priority_fee_lamports: 10_000,
            enable_flash_loans: true,
            enable_mev_protection: true,
            min_profit_threshold_lamports: 10_000_000, // 0.01 SOL
            max_flash_loan_amount_sol: 100.0,
            flash_loan_config: FlashLoanConfig::default(),
            atomic_executor_config: AtomicExecutorConfig::default(),
            mev_protection_config: MevProtectionConfig::default(),
        }
    }
}

/// Enhanced result of an arbitrage execution attempt
#[derive(Clone, Debug)]
pub struct ExecutionResult {
    pub opportunity_id: String,
    pub success: bool,
    pub signature: Option<Signature>,
    pub actual_profit_sol: f64,
    pub actual_gas_cost: u64,
    pub execution_time: Duration,
    pub error_message: Option<String>,
    pub slippage_pct: f64,
    pub flash_loan_used: bool,
    pub flash_loan_fee: Option<u64>,
    pub mev_protection_used: bool,
    pub strategy_used: Option<SubmissionStrategy>,
}

/// Enhanced execution engine for arbitrage opportunities with flash loans and MEV protection
pub struct ExecutionEngine {
    config: ExecutionEngineConfig,
    app_state: Arc<EnhancedAppState>,
    opportunity_queue: Arc<OpportunityQueue>,
    rate_limiter: Arc<RateLimiter>,
    circuit_breaker: Arc<CircuitBreaker>,
    retry_config: RetryConfig,
    execution_semaphore: Arc<Semaphore>,
    swap_config: SwapConfig,
    flash_loan_manager: Arc<FlashLoanManager>,
    atomic_executor: Arc<AtomicExecutor>,
    mev_protection_manager: Arc<MevProtectionManager>,
    transaction_builder: Arc<TransactionBuilder>,
}

impl ExecutionEngine {
    /// Create a new enhanced execution engine
    pub fn new(
        config: ExecutionEngineConfig,
        app_state: Arc<EnhancedAppState>,
        opportunity_queue: Arc<OpportunityQueue>,
        rate_limiter: Arc<RateLimiter>,
        circuit_breaker: Arc<CircuitBreaker>,
        retry_config: RetryConfig,
        swap_config: SwapConfig,
    ) -> Self {
        let execution_semaphore = Arc::new(Semaphore::new(config.max_concurrent_executions));

        // Initialize transaction builder
        let transaction_builder = Arc::new(TransactionBuilder::new(app_state.clone()));

        // Initialize flash loan manager
        let flash_loan_manager = Arc::new(FlashLoanManager::new(
            config.flash_loan_config.clone(),
            app_state.clone(),
            rate_limiter.clone(),
            circuit_breaker.clone(),
        ));

        // Initialize MEV protection manager
        let mev_protection_manager = Arc::new(MevProtectionManager::new(
            config.mev_protection_config.clone(),
            app_state.clone(),
            rate_limiter.clone(),
            circuit_breaker.clone(),
        ));

        // Initialize atomic executor
        let atomic_executor = Arc::new(AtomicExecutor::new(
            config.atomic_executor_config.clone(),
            app_state.clone(),
            flash_loan_manager.clone(),
            rate_limiter.clone(),
            circuit_breaker.clone(),
            transaction_builder.clone(),
        ));

        Self {
            config,
            app_state,
            opportunity_queue,
            rate_limiter,
            circuit_breaker,
            retry_config,
            execution_semaphore,
            swap_config,
            flash_loan_manager,
            atomic_executor,
            mev_protection_manager,
            transaction_builder,
        }
    }

    /// Start the enhanced execution engine main loop
    pub async fn start(&self) -> Result<()> {
        println!("🚀 {} Enhanced Arbitrage Execution Engine with Flash Loans & MEV Protection",
                 "Starting".green().bold());

        // Print configuration summary
        self.print_configuration_summary();

        loop {
            // Get the next highest priority opportunity
            if let Some(prioritized_opportunity) = self.opportunity_queue.get_next_opportunity().await {
                // Check if we have available execution slots
                if self.execution_semaphore.available_permits() > 0 {
                    // Spawn execution task
                    let engine_clone = self.clone();
                    tokio::spawn(async move {
                        if let Err(e) = engine_clone.execute_opportunity(prioritized_opportunity).await {
                            eprintln!("❌ {} Execution error: {}", "Error".red().bold(), e);
                        }
                    });
                } else {
                    // All execution slots busy, put opportunity back and wait
                    self.opportunity_queue.add_opportunity(prioritized_opportunity.opportunity).await;
                    tokio::time::sleep(Duration::from_millis(50)).await;
                }
            } else {
                // No opportunities available, wait a bit
                tokio::time::sleep(Duration::from_millis(100)).await;
            }
        }
    }

    /// Print configuration summary
    fn print_configuration_summary(&self) {
        println!("\n{}", "═".repeat(60).blue());
        println!("🔧 {} Execution Engine Configuration", "Enhanced".yellow().bold());
        println!("{}", "═".repeat(60).blue());

        println!("⚡ Max Concurrent Executions: {}", self.config.max_concurrent_executions);
        println!("⏱️  Execution Timeout: {}s", self.config.execution_timeout_seconds);
        println!("📊 Max Slippage: {:.2}%", self.config.max_slippage_bps as f64 / 100.0);
        println!("💰 Min Profit Threshold: {} lamports", self.config.min_profit_threshold_lamports);

        println!("\n🏦 Flash Loan Features:");
        println!("  • Enabled: {}", if self.config.enable_flash_loans { "✅" } else { "❌" });
        if self.config.enable_flash_loans {
            println!("  • Max Loan Amount: {} SOL", self.config.max_flash_loan_amount_sol);
            println!("  • Providers: {} enabled", self.config.flash_loan_config.enabled_providers.len());
        }

        println!("\n🛡️  MEV Protection:");
        println!("  • Enabled: {}", if self.config.enable_mev_protection { "✅" } else { "❌" });
        if self.config.enable_mev_protection {
            println!("  • Jito Integration: {}", if self.config.mev_protection_config.enable_jito { "✅" } else { "❌" });
            println!("  • Private Mempool: {}", if self.config.mev_protection_config.enable_private_mempool { "✅" } else { "❌" });
        }

        println!("\n🔬 Advanced Features:");
        println!("  • Simulation: {}", if self.config.enable_simulation { "✅" } else { "❌" });
        println!("  • Bundling: {}", if self.config.enable_bundling { "✅" } else { "❌" });
        println!("  • Priority Fee: {} lamports", self.config.priority_fee_lamports);

        println!("{}", "═".repeat(60).blue());
    }

    /// Execute a single arbitrage opportunity with enhanced features
    async fn execute_opportunity(&self, prioritized_opportunity: PrioritizedOpportunity) -> Result<()> {
        let opportunity = &prioritized_opportunity.opportunity;
        let start_time = Instant::now();

        println!("🎯 {} opportunity: {} (Score: {:.4})",
                 "Executing".yellow().bold(),
                 opportunity.id,
                 prioritized_opportunity.composite_score);

        // Acquire execution semaphore
        let _permit = self.execution_semaphore.acquire().await
            .map_err(|_| anyhow!("Failed to acquire execution permit"))?;

        // Check if opportunity meets minimum profit threshold
        if opportunity.expected_profit_sol * 1_000_000_000.0 < self.config.min_profit_threshold_lamports as f64 {
            println!("⚠️  {} Opportunity below profit threshold, skipping", "Warning".yellow());
            return Ok(());
        }

        // Execute with circuit breaker protection
        let result = self.circuit_breaker.execute(|| async {
            self.execute_enhanced_arbitrage(opportunity).await
        }).await;

        let execution_time = start_time.elapsed();

        match result {
            Ok(execution_result) => {
                println!("✅ {} Execution completed in {:.2}ms",
                         "Success".green().bold(),
                         execution_time.as_millis());

                // Update performance metrics
                self.opportunity_queue.update_performance_metrics(
                    opportunity,
                    execution_result.success,
                    execution_result.actual_profit_sol,
                    execution_result.actual_gas_cost,
                    execution_time,
                ).await?;

                Ok(())
            },
            Err(e) => {
                println!("❌ {} Execution failed after {:.2}ms: {}",
                         "Error".red().bold(),
                         execution_time.as_millis(),
                         e);

                // Update performance metrics for failure
                self.opportunity_queue.update_performance_metrics(
                    opportunity,
                    false,
                    0.0,
                    0,
                    execution_time,
                ).await?;

                Err(e)
            }
        }
    }

    /// Enhanced arbitrage execution with flash loans and MEV protection
    async fn execute_enhanced_arbitrage(&self, opportunity: &ArbitrageOpportunity) -> Result<ExecutionResult> {
        let start_time = Instant::now();

        println!("🔍 {} arbitrage strategy: {:?}",
                 "Analyzing".cyan().bold(),
                 opportunity.strategy_type);

        // Determine if flash loan is needed
        let needs_flash_loan = self.requires_flash_loan(opportunity).await?;

        let execution_result = if needs_flash_loan && self.config.enable_flash_loans {
            println!("🏦 {} flash loan execution", "Initiating".blue().bold());
            self.execute_with_flash_loan(opportunity).await
        } else {
            println!("💰 {} standard execution", "Initiating".green().bold());
            self.execute_without_flash_loan(opportunity).await
        };

        match execution_result {
            Ok(mut result) => {
                result.execution_time = start_time.elapsed();

                if result.success {
                    println!("✅ {} Arbitrage executed successfully!", "Success".green().bold());
                    println!("   💰 Profit: {:.6} SOL", result.actual_profit_sol);
                    println!("   ⛽ Gas Cost: {} lamports", result.actual_gas_cost);
                    println!("   📊 Slippage: {:.2}%", result.slippage_pct);
                    if result.flash_loan_used {
                        println!("   🏦 Flash Loan Fee: {} lamports", result.flash_loan_fee.unwrap_or(0));
                    }
                    if result.mev_protection_used {
                        println!("   🛡️  MEV Protection: {:?}", result.strategy_used);
                    }
                } else {
                    println!("❌ {} Arbitrage execution failed: {}",
                             "Failed".red().bold(),
                             result.error_message.as_deref().unwrap_or("Unknown error"));
                }

                Ok(result)
            },
            Err(e) => {
                let error_result = ExecutionResult {
                    opportunity_id: opportunity.id.clone(),
                    success: false,
                    signature: None,
                    actual_profit_sol: 0.0,
                    actual_gas_cost: 0,
                    execution_time: start_time.elapsed(),
                    error_message: Some(e.to_string()),
                    slippage_pct: 0.0,
                    flash_loan_used: false,
                    flash_loan_fee: None,
                    mev_protection_used: false,
                    strategy_used: None,
                };

                Ok(error_result)
            }
        }
    }

    /// Check if the opportunity requires a flash loan
    async fn requires_flash_loan(&self, opportunity: &ArbitrageOpportunity) -> Result<bool> {
        // Calculate required capital for the arbitrage
        let required_capital_sol = self.calculate_required_capital(opportunity).await?;

        // Get current wallet balance
        let wallet_balance = self.app_state.rpc_nonblocking_client
            .get_balance(&self.app_state.wallet.pubkey())
            .await? as f64 / 1_000_000_000.0; // Convert lamports to SOL

        // Need flash loan if required capital exceeds available balance (with safety margin)
        let safety_margin = 0.1; // Keep 0.1 SOL for fees
        let available_capital = wallet_balance - safety_margin;

        let needs_flash_loan = required_capital_sol > available_capital;

        if needs_flash_loan {
            println!("🏦 Flash loan required: {:.6} SOL needed, {:.6} SOL available",
                     required_capital_sol, available_capital);
        } else {
            println!("💰 Sufficient balance: {:.6} SOL needed, {:.6} SOL available",
                     required_capital_sol, available_capital);
        }

        Ok(needs_flash_loan)
    }

    /// Calculate required capital for the arbitrage
    async fn calculate_required_capital(&self, opportunity: &ArbitrageOpportunity) -> Result<f64> {
        // This is a simplified calculation - in production this would be more sophisticated
        match &opportunity.strategy_type {
            ArbitrageStrategy::MultiDex => {
                // For multi-DEX arbitrage, we need the input amount
                Ok(opportunity.input_amount_sol)
            },
            ArbitrageStrategy::TwoHop { .. } => {
                // For two-hop arbitrage, we need the initial input
                Ok(opportunity.input_amount_sol)
            },
            ArbitrageStrategy::Triangle { .. } => {
                // For triangle arbitrage, we typically start with the base token
                Ok(opportunity.input_amount_sol)
            },
        }
    }

    /// Execute arbitrage with flash loan
    async fn execute_with_flash_loan(&self, opportunity: &ArbitrageOpportunity) -> Result<ExecutionResult> {
        let required_amount_sol = self.calculate_required_capital(opportunity).await?;
        let required_amount_lamports = (required_amount_sol * 1_000_000_000.0) as u64;

        // Create swap steps for the arbitrage
        let swap_steps = self.create_swap_steps(opportunity).await?;

        // Create atomic swap request
        let atomic_request = AtomicSwapRequest {
            token_in: opportunity.token_mint,
            token_out: opportunity.token_mint, // Will be updated based on strategy
            amount_in: required_amount_lamports,
            min_amount_out: (opportunity.estimated_profit_sol * 1_000_000_000.0) as u64,
            swap_path: swap_steps,
            flash_loan_amount: Some(required_amount_lamports),
            max_slippage_bps: (self.config.max_slippage_pct * 100.0) as u64,
            expected_profit_lamports: (opportunity.estimated_profit_sol * 1_000_000_000.0) as u64,
        };

        // Execute with flash loan through atomic executor
        let request_id = format!("flash_loan_{}", opportunity.id);
        let swap_instructions = Vec::new(); // Will be built by atomic executor
        let atomic_result = self.atomic_executor.execute_with_flash_loan(
            &request_id,
            &atomic_request,
            swap_instructions,
            required_amount_lamports,
        ).await?;

        // Apply MEV protection if enabled
        let final_result = if self.config.enable_mev_protection {
            self.apply_mev_protection(atomic_result).await?
        } else {
            atomic_result
        };

        // Convert atomic result to execution result
        Ok(ExecutionResult {
            opportunity_id: opportunity.id.clone(),
            success: final_result.success,
            signature: final_result.transaction_signature.and_then(|s| s.parse().ok()),
            actual_profit_sol: final_result.actual_profit_lamports as f64 / 1_000_000_000.0,
            actual_gas_cost: final_result.gas_used,
            execution_time: final_result.execution_time,
            error_message: final_result.error_message,
            slippage_pct: 0.0, // Will be calculated from swap results
            flash_loan_used: true,
            flash_loan_fee: final_result.flash_loan_result.map(|r| r.fee_paid),
            mev_protection_used: self.config.enable_mev_protection,
            strategy_used: Some(SubmissionStrategy::Jito), // Default strategy
        })
    }

    /// Execute arbitrage without flash loan
    async fn execute_without_flash_loan(&self, opportunity: &ArbitrageOpportunity) -> Result<ExecutionResult> {
        // Create swap steps for the arbitrage
        let swap_steps = self.create_swap_steps(opportunity).await?;

        // Create atomic swap request
        let atomic_request = AtomicSwapRequest {
            token_in: opportunity.token_mint,
            token_out: opportunity.token_mint, // Will be updated based on strategy
            amount_in: (opportunity.trade_amount * 1_000_000_000.0) as u64,
            min_amount_out: (opportunity.estimated_profit_sol * 1_000_000_000.0) as u64,
            swap_path: swap_steps,
            flash_loan_amount: None,
            max_slippage_bps: (self.config.max_slippage_pct * 100.0) as u64,
            expected_profit_lamports: (opportunity.estimated_profit_sol * 1_000_000_000.0) as u64,
        };

        // Execute without flash loan through atomic executor
        let request_id = format!("direct_{}", opportunity.id);
        let swap_instructions = Vec::new(); // Will be built by atomic executor
        let atomic_result = self.atomic_executor.execute_without_flash_loan(
            &request_id,
            &atomic_request,
            swap_instructions,
        ).await?;

        // Apply MEV protection if enabled
        let final_result = if self.config.enable_mev_protection {
            self.apply_mev_protection(atomic_result).await?
        } else {
            atomic_result
        };

        // Convert atomic result to execution result
        Ok(ExecutionResult {
            opportunity_id: opportunity.id.clone(),
            success: final_result.success,
            signature: final_result.transaction_signature.and_then(|s| s.parse().ok()),
            actual_profit_sol: final_result.actual_profit_lamports as f64 / 1_000_000_000.0,
            actual_gas_cost: final_result.gas_used,
            execution_time: final_result.execution_time,
            error_message: final_result.error_message,
            slippage_pct: 0.0, // Will be calculated from swap results
            flash_loan_used: false,
            flash_loan_fee: None,
            mev_protection_used: self.config.enable_mev_protection,
            strategy_used: Some(SubmissionStrategy::HighPriorityFee), // Default strategy for direct execution
        })
    }

    /// Create swap steps from arbitrage opportunity
    async fn create_swap_steps(&self, opportunity: &ArbitrageOpportunity) -> Result<Vec<SwapStep>> {
        let mut swap_steps = Vec::new();

        match &opportunity.strategy_type {
            ArbitrageStrategy::MultiDex => {
                // Simple multi-DEX arbitrage: buy on one DEX, sell on another
                swap_steps.push(SwapStep {
                    dex_name: opportunity.buy_dex.clone(),
                    pool_address: opportunity.token_mint, // Placeholder - should get actual pool address
                    token_in: spl_token::native_mint::id(),
                    token_out: opportunity.token_mint,
                    amount_in: opportunity.trade_amount,
                    min_amount_out: 0, // Will be calculated based on slippage
                    pool: Arc::new(crate::pools::PlaceholderPool::new(opportunity.token_mint)),
                });

                swap_steps.push(SwapStep {
                    dex_name: opportunity.sell_dex.clone(),
                    pool_address: opportunity.token_mint, // Placeholder - should get actual pool address
                    token_in: opportunity.token_mint,
                    token_out: spl_token::native_mint::id(),
                    amount_in: 0, // Will be set to output of previous step
                    min_amount_out: 0, // Will be calculated based on slippage
                    pool: Arc::new(crate::pools::PlaceholderPool::new(opportunity.token_mint)),
                });
            },
            ArbitrageStrategy::TwoHop { intermediate_token } => {
                // Two-hop arbitrage: A -> B -> A
                swap_steps.push(SwapStep {
                    dex_name: opportunity.buy_dex.clone(),
                    pool_address: opportunity.token_mint, // Placeholder
                    token_in: opportunity.token_mint,
                    token_out: *intermediate_token,
                    amount_in: opportunity.trade_amount,
                    min_amount_out: 0,
                    pool: Arc::new(crate::pools::PlaceholderPool::new(opportunity.token_mint)),
                });

                swap_steps.push(SwapStep {
                    dex_name: opportunity.sell_dex.clone(),
                    pool_address: *intermediate_token, // Placeholder
                    token_in: *intermediate_token,
                    token_out: opportunity.token_mint,
                    amount_in: 0,
                    min_amount_out: 0,
                    pool: Arc::new(crate::pools::PlaceholderPool::new(*intermediate_token)),
                });
            },
            ArbitrageStrategy::Triangle { token_b, token_c } => {
                // Triangle arbitrage: A -> B -> C -> A
                swap_steps.push(SwapStep {
                    dex_name: opportunity.buy_dex.clone(),
                    pool_address: opportunity.token_mint, // Placeholder
                    token_in: opportunity.token_mint,
                    token_out: *token_b,
                    amount_in: opportunity.trade_amount,
                    min_amount_out: 0,
                    pool: Arc::new(crate::pools::PlaceholderPool::new(opportunity.token_mint)),
                });

                // This would need additional pool information for the second and third swaps
                // For now, simplified to two steps
                swap_steps.push(SwapStep {
                    dex_name: opportunity.sell_dex.clone(),
                    pool_address: *token_b, // Placeholder
                    token_in: *token_b,
                    token_out: opportunity.token_mint,
                    amount_in: 0,
                    min_amount_out: 0,
                    pool: Arc::new(crate::pools::PlaceholderPool::new(*token_b)),
                });
            },
        }

        Ok(swap_steps)
    }

    /// Apply MEV protection to atomic execution result
    async fn apply_mev_protection(&self, atomic_result: crate::core::atomic_executor::AtomicSwapResult) -> Result<crate::core::atomic_executor::AtomicSwapResult> {
        // This is a placeholder - in a real implementation, this would integrate with the MEV protection
        // For now, just return the result as-is since AtomicSwapResult doesn't have mev_strategy_used field
        Ok(atomic_result)
    }

    /// Get execution engine statistics
    pub async fn get_execution_stats(&self) -> ExecutionEngineStats {
        ExecutionEngineStats {
            available_execution_slots: self.execution_semaphore.available_permits(),
            max_execution_slots: self.config.max_concurrent_executions,
            flash_loans_enabled: self.config.enable_flash_loans,
            mev_protection_enabled: self.config.enable_mev_protection,
            min_profit_threshold_sol: self.config.min_profit_threshold_lamports as f64 / 1_000_000_000.0,
            last_updated: Instant::now(),
        }
    }
}

// Clone implementation for ExecutionEngine
impl Clone for ExecutionEngine {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            app_state: self.app_state.clone(),
            opportunity_queue: self.opportunity_queue.clone(),
            rate_limiter: self.rate_limiter.clone(),
            circuit_breaker: self.circuit_breaker.clone(),
            retry_config: self.retry_config.clone(),
            execution_semaphore: self.execution_semaphore.clone(),
            swap_config: self.swap_config.clone(),
            flash_loan_manager: self.flash_loan_manager.clone(),
            atomic_executor: self.atomic_executor.clone(),
            mev_protection_manager: self.mev_protection_manager.clone(),
            transaction_builder: self.transaction_builder.clone(),
        }
    }
}

/// Execution engine statistics
#[derive(Debug, Clone)]
pub struct ExecutionEngineStats {
    pub available_execution_slots: usize,
    pub max_execution_slots: usize,
    pub flash_loans_enabled: bool,
    pub mev_protection_enabled: bool,
    pub min_profit_threshold_sol: f64,
    pub last_updated: Instant,
}


