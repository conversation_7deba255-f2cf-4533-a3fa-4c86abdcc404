//! Arbitrage Execution Engine
//! 
//! This module handles the execution of arbitrage opportunities from the priority queue,
//! including transaction building, submission, and result tracking.

use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use anyhow::{Result, anyhow};
use solana_sdk::{
    pubkey::Pubkey,
    signature::{Keypair, Signature},
    signer::Signer,
    transaction::Transaction,
    instruction::Instruction,
};
use solana_client::nonblocking::rpc_client::RpcClient;

use crate::core::priority_queue::{OpportunityQueue, PrioritizedOpportunity, ArbitrageStrategy, ArbitrageOpportunity};
use crate::core::rate_limiter::RateLimiter;
use crate::core::circuit_breaker::CircuitBreaker;
use crate::core::retry::{retry_with_backoff, RetryConfig};
use crate::common::config::SwapConfig;

/// Configuration for the execution engine
#[derive(Clone, Debug)]
pub struct ExecutionEngineConfig {
    pub max_concurrent_executions: usize,
    pub execution_timeout_seconds: u64,
    pub max_slippage_bps: u64,
    pub enable_simulation: bool,
    pub enable_bundling: bool,
    pub priority_fee_lamports: u64,
}

impl Default for ExecutionEngineConfig {
    fn default() -> Self {
        Self {
            max_concurrent_executions: 3,
            execution_timeout_seconds: 30,
            max_slippage_bps: 100, // 1%
            enable_simulation: true,
            enable_bundling: false,
            priority_fee_lamports: 10_000,
        }
    }
}

/// Result of an arbitrage execution attempt
#[derive(Clone, Debug)]
pub struct ExecutionResult {
    pub opportunity_id: String,
    pub success: bool,
    pub signature: Option<Signature>,
    pub actual_profit_sol: f64,
    pub actual_gas_cost: u64,
    pub execution_time: Duration,
    pub error_message: Option<String>,
    pub slippage_pct: f64,
}

/// Main execution engine for arbitrage opportunities
pub struct ExecutionEngine {
    config: ExecutionEngineConfig,
    opportunity_queue: Arc<OpportunityQueue>,
    rpc_client: Arc<RpcClient>,
    wallet: Arc<Keypair>,
    rate_limiter: Arc<RateLimiter>,
    circuit_breaker: Arc<CircuitBreaker>,
    retry_config: RetryConfig,
    execution_semaphore: Arc<Semaphore>,
    swap_config: SwapConfig,
}

impl ExecutionEngine {
    /// Create a new execution engine
    pub fn new(
        config: ExecutionEngineConfig,
        opportunity_queue: Arc<OpportunityQueue>,
        rpc_client: Arc<RpcClient>,
        wallet: Arc<Keypair>,
        rate_limiter: Arc<RateLimiter>,
        circuit_breaker: Arc<CircuitBreaker>,
        retry_config: RetryConfig,
        swap_config: SwapConfig,
    ) -> Self {
        let execution_semaphore = Arc::new(Semaphore::new(config.max_concurrent_executions));

        Self {
            config,
            opportunity_queue,
            rpc_client,
            wallet,
            rate_limiter,
            circuit_breaker,
            retry_config,
            execution_semaphore,
            swap_config,
        }
    }

    /// Start the execution engine main loop
    pub async fn start(&self) -> Result<()> {
        println!("🚀 Starting Arbitrage Execution Engine");
        
        loop {
            // Get the next highest priority opportunity
            if let Some(prioritized_opportunity) = self.opportunity_queue.get_next_opportunity().await {
                // Spawn execution task
                let engine = self.clone();
                tokio::spawn(async move {
                    if let Err(e) = engine.execute_opportunity(prioritized_opportunity).await {
                        eprintln!("❌ Execution error: {}", e);
                    }
                });
            } else {
                // No opportunities available, wait a bit
                tokio::time::sleep(Duration::from_millis(100)).await;
            }
        }
    }

    /// Execute a single arbitrage opportunity
    async fn execute_opportunity(&self, prioritized_opportunity: PrioritizedOpportunity) -> Result<()> {
        let opportunity = &prioritized_opportunity.opportunity;
        let start_time = Instant::now();

        println!("🎯 Executing opportunity: {} (Score: {:.4})", 
                 opportunity.id, prioritized_opportunity.composite_score);

        // Acquire execution semaphore
        let _permit = self.execution_semaphore.acquire().await
            .map_err(|_| anyhow!("Failed to acquire execution permit"))?;

        // Execute with circuit breaker protection
        let result = self.circuit_breaker.execute(|| async {
            self.execute_arbitrage_internal(opportunity).await
        }).await;

        match result {
            Ok(_) => Ok(()),
            Err(e) => Err(e),
        }
    }

    /// Internal execution method without circuit breaker
    async fn execute_arbitrage_internal(&self, opportunity: &ArbitrageOpportunity) -> Result<()> {
        let start_time = Instant::now();
        let execution_result = match opportunity.strategy_type {
            ArbitrageStrategy::MultiDex => {
                self.execute_multi_dex_arbitrage(opportunity).await
            },
            ArbitrageStrategy::TwoHop { intermediate_token } => {
                self.execute_two_hop_arbitrage(opportunity, intermediate_token).await
            },
            ArbitrageStrategy::Triangle { token_b, token_c } => {
                self.execute_triangle_arbitrage(opportunity, token_b, token_c).await
            },
        };

        let execution_time = start_time.elapsed();

        match execution_result {
            Ok(result) => {
                println!("✅ Execution successful: {} SOL profit", result.actual_profit_sol);
                
                // Update performance metrics
                self.opportunity_queue.update_performance_metrics(
                    opportunity,
                    true,
                    result.actual_profit_sol,
                    result.actual_gas_cost,
                    execution_time,
                ).await?;

                // Circuit breaker success is handled automatically by execute()
            },
            Err(e) => {
                println!("❌ Execution failed: {}", e);
                
                // Update performance metrics
                self.opportunity_queue.update_performance_metrics(
                    opportunity,
                    false,
                    0.0,
                    0,
                    execution_time,
                ).await?;

                // Circuit breaker failure is handled automatically by execute()
                return Err(e);
            }
        }

        Ok(())
    }

    /// Execute multi-DEX arbitrage
    async fn execute_multi_dex_arbitrage(&self, opportunity: &crate::core::priority_queue::ArbitrageOpportunity) -> Result<ExecutionResult> {
        let start_time = Instant::now();

        // Simulate the transaction first if enabled
        if self.config.enable_simulation {
            self.simulate_multi_dex_trade(opportunity).await?;
        }

        // Build buy transaction
        let buy_instructions = self.build_buy_instructions(
            &opportunity.token_mint,
            &opportunity.buy_dex,
            opportunity.trade_amount,
            opportunity.buy_price,
        ).await?;

        // Build sell transaction
        let sell_instructions = self.build_sell_instructions(
            &opportunity.token_mint,
            &opportunity.sell_dex,
            opportunity.trade_amount,
            opportunity.sell_price,
        ).await?;

        // Combine instructions into a single transaction
        let mut all_instructions = Vec::new();
        all_instructions.extend(buy_instructions);
        all_instructions.extend(sell_instructions);

        // Execute transaction with retry logic
        let signature = retry_with_backoff(
            &self.retry_config,
            || self.submit_transaction(&all_instructions),
        ).await?;

        // Wait for confirmation
        self.wait_for_confirmation(&signature).await?;

        // Calculate actual results
        let execution_time = start_time.elapsed();
        let actual_profit_sol = opportunity.estimated_profit_sol; // Simplified - would calculate actual
        let actual_gas_cost = opportunity.estimated_gas_cost; // Simplified - would get actual

        Ok(ExecutionResult {
            opportunity_id: opportunity.id.clone(),
            success: true,
            signature: Some(signature),
            actual_profit_sol,
            actual_gas_cost,
            execution_time,
            error_message: None,
            slippage_pct: 0.0, // Simplified - would calculate actual slippage
        })
    }

    /// Execute two-hop arbitrage
    async fn execute_two_hop_arbitrage(
        &self,
        opportunity: &crate::core::priority_queue::ArbitrageOpportunity,
        intermediate_token: Pubkey,
    ) -> Result<ExecutionResult> {
        // Implementation for two-hop arbitrage
        // This would involve:
        // 1. Token A -> Intermediate Token
        // 2. Intermediate Token -> Token A
        
        // For now, return a placeholder
        Err(anyhow!("Two-hop arbitrage execution not yet implemented"))
    }

    /// Execute triangle arbitrage
    async fn execute_triangle_arbitrage(
        &self,
        opportunity: &crate::core::priority_queue::ArbitrageOpportunity,
        token_b: Pubkey,
        token_c: Pubkey,
    ) -> Result<ExecutionResult> {
        // Implementation for triangle arbitrage
        // This would involve:
        // 1. Token A -> Token B
        // 2. Token B -> Token C
        // 3. Token C -> Token A
        
        // For now, return a placeholder
        Err(anyhow!("Triangle arbitrage execution not yet implemented"))
    }

    /// Simulate a multi-DEX trade before execution
    async fn simulate_multi_dex_trade(&self, opportunity: &crate::core::priority_queue::ArbitrageOpportunity) -> Result<()> {
        // Use RPC simulate transaction to verify the trade would succeed
        // This helps avoid failed transactions and wasted gas
        
        println!("🧪 Simulating trade for opportunity: {}", opportunity.id);
        
        // For now, just return success
        // In practice, you would build the transaction and call simulate_transaction
        Ok(())
    }

    /// Build buy instructions for a specific DEX
    async fn build_buy_instructions(
        &self,
        token_mint: &Pubkey,
        dex: &str,
        amount: u64,
        expected_price: f64,
    ) -> Result<Vec<Instruction>> {
        // This would integrate with specific DEX SDKs to build swap instructions
        // For now, return empty vector
        println!("🔨 Building buy instructions for {} on {}", token_mint, dex);
        Ok(vec![])
    }

    /// Build sell instructions for a specific DEX
    async fn build_sell_instructions(
        &self,
        token_mint: &Pubkey,
        dex: &str,
        amount: u64,
        expected_price: f64,
    ) -> Result<Vec<Instruction>> {
        // This would integrate with specific DEX SDKs to build swap instructions
        // For now, return empty vector
        println!("🔨 Building sell instructions for {} on {}", token_mint, dex);
        Ok(vec![])
    }

    /// Submit transaction to the blockchain
    async fn submit_transaction(&self, instructions: &[Instruction]) -> Result<Signature> {
        // Rate limit the submission
        // Rate limiting is handled by execute_rpc wrapper

        // Get recent blockhash
        let recent_blockhash = self.rpc_client.get_latest_blockhash().await?;

        // Build transaction
        let transaction = Transaction::new_signed_with_payer(
            instructions,
            Some(&self.wallet.pubkey()),
            &[&*self.wallet],
            recent_blockhash,
        );

        // Submit transaction
        let signature = self.rpc_client.send_transaction(&transaction).await?;
        
        println!("📤 Transaction submitted: {}", signature);
        Ok(signature)
    }

    /// Wait for transaction confirmation
    async fn wait_for_confirmation(&self, signature: &Signature) -> Result<()> {
        let timeout = Duration::from_secs(self.config.execution_timeout_seconds);
        let start_time = Instant::now();

        while start_time.elapsed() < timeout {
            match self.rpc_client.get_signature_status(signature).await? {
                Some(Ok(_)) => {
                    println!("✅ Transaction confirmed: {}", signature);
                    return Ok(());
                },
                Some(Err(e)) => {
                    return Err(anyhow!("Transaction failed: {}", e));
                },
                None => {
                    // Still pending, wait a bit
                    tokio::time::sleep(Duration::from_millis(500)).await;
                }
            }
        }

        Err(anyhow!("Transaction confirmation timeout"))
    }

    /// Get execution statistics
    pub async fn get_execution_stats(&self) -> ExecutionStats {
        let queue_stats = self.opportunity_queue.get_queue_stats().await;
        let performance_metrics = self.opportunity_queue.get_performance_metrics().await;

        ExecutionStats {
            pending_opportunities: queue_stats.total_opportunities,
            total_trades: performance_metrics.total_trades,
            successful_trades: performance_metrics.successful_trades,
            success_rate: if performance_metrics.total_trades > 0 {
                performance_metrics.successful_trades as f64 / performance_metrics.total_trades as f64
            } else {
                0.0
            },
            total_profit_sol: performance_metrics.total_profit_sol,
            available_execution_slots: self.execution_semaphore.available_permits(),
        }
    }
}

// Implement Clone for ExecutionEngine to allow spawning tasks
impl Clone for ExecutionEngine {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            opportunity_queue: Arc::clone(&self.opportunity_queue),
            rpc_client: Arc::clone(&self.rpc_client),
            wallet: Arc::clone(&self.wallet),
            rate_limiter: Arc::clone(&self.rate_limiter),
            circuit_breaker: Arc::clone(&self.circuit_breaker),
            retry_config: self.retry_config.clone(),
            execution_semaphore: Arc::clone(&self.execution_semaphore),
            swap_config: self.swap_config.clone(),
        }
    }
}

/// Statistics about execution engine performance
#[derive(Clone, Debug)]
pub struct ExecutionStats {
    pub pending_opportunities: usize,
    pub total_trades: u64,
    pub successful_trades: u64,
    pub success_rate: f64,
    pub total_profit_sol: f64,
    pub available_execution_slots: usize,
}
