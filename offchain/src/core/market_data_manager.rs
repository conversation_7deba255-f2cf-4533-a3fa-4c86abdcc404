//! Market Data Manager - Phase 2 Integration
//! 
//! This module integrates all Phase 2 components including price feeds, pool monitoring,
//! market depth analysis, price impact calculation, and volatility detection into a
//! unified real-time market data system.

use std::{
    collections::HashMap,
    sync::Arc,
    time::Duration,
};
use anyhow::{Result, anyhow};
use tokio::{
    sync::{RwLock, mpsc},
    time::interval,
};
use serde::{Deserialize, Serialize};
use anchor_client::solana_sdk::pubkey::Pubkey;
use colored::Colorize;

use crate::core::{
    app_state::EnhancedAppState,
    price_feed::{PriceFeedManager, PriceFeedConfig},
    pool_monitor::{PoolMonitor, PoolMonitorConfig},
    market_depth::{MarketDepthAnalyzer, MarketDepthConfig},
    price_impact::{PriceImpactCalculator, PriceImpactConfig},
    volatility_detector::{VolatilityDetector, VolatilityConfig},
};

/// Market data manager configuration
#[derive(Debug, Clone)]
pub struct MarketDataConfig {
    pub price_feed_config: PriceFeedConfig,
    pub pool_monitor_config: PoolMonitorConfig,
    pub market_depth_config: MarketDepthConfig,
    pub price_impact_config: PriceImpactConfig,
    pub volatility_config: VolatilityConfig,
    pub update_interval_ms: u64,
    pub enable_real_time_alerts: bool,
    pub alert_thresholds: AlertThresholds,
}

/// Alert thresholds for market conditions
#[derive(Debug, Clone)]
pub struct AlertThresholds {
    pub high_volatility_threshold: f64,
    pub low_liquidity_threshold: u64,
    pub high_price_impact_threshold: f64,
    pub significant_price_change_threshold: f64,
}

impl Default for AlertThresholds {
    fn default() -> Self {
        Self {
            high_volatility_threshold: 15.0,  // 15%
            low_liquidity_threshold: 50000,   // 50k tokens
            high_price_impact_threshold: 5.0, // 5%
            significant_price_change_threshold: 10.0, // 10%
        }
    }
}

impl Default for MarketDataConfig {
    fn default() -> Self {
        Self {
            price_feed_config: PriceFeedConfig::default(),
            pool_monitor_config: PoolMonitorConfig::default(),
            market_depth_config: MarketDepthConfig::default(),
            price_impact_config: PriceImpactConfig::default(),
            volatility_config: VolatilityConfig::default(),
            update_interval_ms: 3000, // 3 seconds
            enable_real_time_alerts: true,
            alert_thresholds: AlertThresholds::default(),
        }
    }
}

/// Market alert types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MarketAlert {
    HighVolatility {
        token: String,
        volatility: f64,
        threshold: f64,
    },
    LowLiquidity {
        token_pair: (Pubkey, Pubkey),
        liquidity: u64,
        threshold: u64,
    },
    HighPriceImpact {
        token_pair: (Pubkey, Pubkey),
        impact: f64,
        threshold: f64,
    },
    SignificantPriceChange {
        token: String,
        price_change: f64,
        threshold: f64,
    },
    PoolStateChange {
        pool_id: Pubkey,
        change_type: String,
    },
}

/// Market data summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketDataSummary {
    pub total_monitored_tokens: usize,
    pub total_monitored_pools: usize,
    pub average_volatility: f64,
    pub total_liquidity_usd: f64,
    pub active_alerts: Vec<MarketAlert>,
    pub system_health: SystemHealth,
    pub last_updated: u64,
}

/// System health status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemHealth {
    pub price_feed_status: ComponentStatus,
    pub pool_monitor_status: ComponentStatus,
    pub volatility_detector_status: ComponentStatus,
    pub overall_status: ComponentStatus,
}

/// Component status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum ComponentStatus {
    Healthy,
    Warning,
    Error,
    Offline,
}

/// Integrated market data manager
pub struct MarketDataManager {
    config: MarketDataConfig,
    app_state: Arc<EnhancedAppState>,
    
    // Core components
    price_feed: Arc<PriceFeedManager>,
    pool_monitor: Arc<PoolMonitor>,
    market_depth_analyzer: Arc<MarketDepthAnalyzer>,
    price_impact_calculator: Arc<PriceImpactCalculator>,
    volatility_detector: Arc<VolatilityDetector>,
    
    // State management
    active_alerts: Arc<RwLock<Vec<MarketAlert>>>,
    system_stats: Arc<RwLock<MarketDataSummary>>,
    
    // Communication channels
    alert_sender: mpsc::UnboundedSender<MarketAlert>,
    alert_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<MarketAlert>>>>,
}

impl MarketDataManager {
    /// Create a new market data manager
    pub async fn new(
        config: MarketDataConfig,
        app_state: Arc<EnhancedAppState>,
    ) -> Result<Self> {
        println!("{}", "🚀 Initializing Market Data Manager".blue().bold());
        println!("{}", "═".repeat(60).blue());

        // Initialize price feed manager
        let price_feed = Arc::new(
            PriceFeedManager::new(config.price_feed_config.clone(), app_state.clone()).await?
        );

        // Initialize pool monitor
        let pool_monitor = Arc::new(
            PoolMonitor::new(config.pool_monitor_config.clone(), app_state.clone()).await?
        );

        // Initialize market depth analyzer
        let market_depth_analyzer = Arc::new(
            MarketDepthAnalyzer::new(
                config.market_depth_config.clone(),
                pool_monitor.clone(),
                app_state.clone(),
            )
        );

        // Initialize price impact calculator
        let price_impact_calculator = Arc::new(
            PriceImpactCalculator::new(config.price_impact_config.clone())
        );

        // Initialize volatility detector
        let volatility_detector = Arc::new(
            VolatilityDetector::new(
                config.volatility_config.clone(),
                price_feed.clone(),
                app_state.clone(),
            )
        );

        let (alert_sender, alert_receiver) = mpsc::unbounded_channel();

        println!("✅ All market data components initialized");

        Ok(Self {
            config,
            app_state,
            price_feed,
            pool_monitor,
            market_depth_analyzer,
            price_impact_calculator,
            volatility_detector,
            active_alerts: Arc::new(RwLock::new(Vec::new())),
            system_stats: Arc::new(RwLock::new(MarketDataSummary {
                total_monitored_tokens: 0,
                total_monitored_pools: 0,
                average_volatility: 0.0,
                total_liquidity_usd: 0.0,
                active_alerts: Vec::new(),
                system_health: SystemHealth {
                    price_feed_status: ComponentStatus::Offline,
                    pool_monitor_status: ComponentStatus::Offline,
                    volatility_detector_status: ComponentStatus::Offline,
                    overall_status: ComponentStatus::Offline,
                },
                last_updated: chrono::Utc::now().timestamp_millis() as u64,
            })),
            alert_sender,
            alert_receiver: Arc::new(RwLock::new(Some(alert_receiver))),
        })
    }

    /// Start the market data manager
    pub async fn start(&self) -> Result<()> {
        println!("{}", "🚀 Starting Market Data Manager".green().bold());
        println!("{}", "═".repeat(60).green());

        // Start all components
        println!("📡 Starting price feed manager...");
        self.price_feed.start().await?;

        println!("🔍 Starting pool monitor...");
        self.pool_monitor.start().await?;

        println!("📊 Starting market depth analyzer...");
        self.market_depth_analyzer.start().await?;

        println!("📈 Starting volatility detector...");
        self.volatility_detector.start().await?;

        // Start alert processing
        if self.config.enable_real_time_alerts {
            self.start_alert_processor().await?;
        }

        // Start periodic updates
        self.start_periodic_updates().await?;

        // Start system health monitoring
        self.start_health_monitoring().await?;

        println!("✅ Market Data Manager started successfully");
        self.print_startup_summary().await;

        Ok(())
    }

    /// Add a token pair for comprehensive monitoring
    pub async fn add_token_pair(&self, token_a: Pubkey, token_b: Pubkey) -> Result<()> {
        println!("📊 Adding token pair {}/{} to comprehensive monitoring", token_a, token_b);

        // Add to volatility monitoring
        self.volatility_detector.add_token(token_a.to_string()).await?;
        self.volatility_detector.add_token(token_b.to_string()).await?;

        // Pool monitoring will automatically pick up pools for this pair
        // when they are discovered

        println!("✅ Token pair added to monitoring");
        Ok(())
    }

    /// Get comprehensive market analysis for a token pair
    pub async fn get_comprehensive_analysis(
        &self,
        token_a: &Pubkey,
        token_b: &Pubkey,
        trade_amount: u64,
        trade_direction: crate::core::market_depth::TradeDirection,
    ) -> Result<ComprehensiveAnalysis> {
        // Get pool states
        let pools = self.pool_monitor.get_pools_by_tokens(token_a, token_b).await;
        
        // Get market depth
        let market_depth = self.market_depth_analyzer.analyze_depth(token_a, token_b).await?;
        
        // Calculate price impact
        let price_impact = self.price_impact_calculator
            .calculate_impact(&pools, &trade_direction, trade_amount, token_a, token_b)
            .await?;
        
        // Get volatility analysis
        let token_a_volatility = self.volatility_detector
            .get_volatility_analysis(&token_a.to_string())
            .await;
        let token_b_volatility = self.volatility_detector
            .get_volatility_analysis(&token_b.to_string())
            .await;

        // Get trade size recommendation
        let trade_size_rec = self.market_depth_analyzer
            .get_trade_size_recommendation(token_a, token_b, trade_direction.clone(), trade_amount)
            .await?;

        Ok(ComprehensiveAnalysis {
            token_pair: (*token_a, *token_b),
            pools,
            market_depth,
            price_impact: price_impact.clone(),
            token_a_volatility: token_a_volatility.clone(),
            token_b_volatility: token_b_volatility.clone(),
            trade_size_recommendation: trade_size_rec,
            trading_safety_score: self.calculate_trading_safety_score(
                &token_a_volatility,
                &token_b_volatility,
                &price_impact,
            ).await,
        })
    }

    /// Start alert processing
    async fn start_alert_processor(&self) -> Result<()> {
        let mut receiver = self.alert_receiver.write().await
            .take()
            .ok_or_else(|| anyhow!("Alert processor already started"))?;

        let active_alerts = self.active_alerts.clone();

        tokio::spawn(async move {
            while let Some(alert) = receiver.recv().await {
                println!("🚨 Market Alert: {:?}", alert);
                
                let mut alerts = active_alerts.write().await;
                alerts.push(alert);
                
                // Keep only recent alerts (last 100)
                if alerts.len() > 100 {
                    let drain_count = alerts.len() - 100;
                    alerts.drain(0..drain_count);
                }
            }
        });

        Ok(())
    }

    /// Start periodic updates
    async fn start_periodic_updates(&self) -> Result<()> {
        let system_stats = self.system_stats.clone();
        let price_feed = self.price_feed.clone();
        let pool_monitor = self.pool_monitor.clone();
        let volatility_detector = self.volatility_detector.clone();
        let update_interval = self.config.update_interval_ms;

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_millis(update_interval));
            
            loop {
                interval.tick().await;
                
                // Update system statistics
                let price_stats = price_feed.get_price_stats().await;
                let pool_stats = pool_monitor.get_stats().await;
                let volatility_stats = volatility_detector.get_volatility_stats().await;
                
                let mut stats = system_stats.write().await;
                stats.total_monitored_tokens = price_stats.len();
                stats.total_monitored_pools = *pool_stats.get("total_monitored").unwrap_or(&0) as usize;
                stats.average_volatility = *volatility_stats.get("avg_volatility").unwrap_or(&0.0);
                stats.last_updated = chrono::Utc::now().timestamp_millis() as u64;
            }
        });

        Ok(())
    }

    /// Start health monitoring
    async fn start_health_monitoring(&self) -> Result<()> {
        let system_stats = self.system_stats.clone();

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(30)); // Check every 30 seconds
            
            loop {
                interval.tick().await;
                
                let mut stats = system_stats.write().await;
                
                // Update component health status
                stats.system_health.price_feed_status = ComponentStatus::Healthy; // TODO: Implement actual health checks
                stats.system_health.pool_monitor_status = ComponentStatus::Healthy;
                stats.system_health.volatility_detector_status = ComponentStatus::Healthy;
                
                // Determine overall status
                stats.system_health.overall_status = if [
                    &stats.system_health.price_feed_status,
                    &stats.system_health.pool_monitor_status,
                    &stats.system_health.volatility_detector_status,
                ].iter().all(|&status| status == &ComponentStatus::Healthy) {
                    ComponentStatus::Healthy
                } else {
                    ComponentStatus::Warning
                };
            }
        });

        Ok(())
    }

    /// Calculate trading safety score
    async fn calculate_trading_safety_score(
        &self,
        token_a_volatility: &Option<crate::core::volatility_detector::VolatilityAnalysis>,
        token_b_volatility: &Option<crate::core::volatility_detector::VolatilityAnalysis>,
        price_impact: &crate::core::price_impact::PriceImpactAnalysis,
    ) -> f64 {
        let mut safety_score = 1.0;

        // Factor in volatility
        if let Some(vol_a) = token_a_volatility {
            safety_score *= 1.0 - vol_a.risk_score * 0.3;
        }
        if let Some(vol_b) = token_b_volatility {
            safety_score *= 1.0 - vol_b.risk_score * 0.3;
        }

        // Factor in price impact
        let impact_factor = (price_impact.estimated_impact / 10.0).min(1.0);
        safety_score *= 1.0 - impact_factor * 0.4;

        safety_score.max(0.0).min(1.0)
    }

    /// Print startup summary
    async fn print_startup_summary(&self) {
        println!("{}", "\n📊 Market Data Manager Status".green().bold());
        println!("{}", "─".repeat(40).green());
        println!("✅ Price Feed Manager: Active");
        println!("✅ Pool Monitor: Active");
        println!("✅ Market Depth Analyzer: Active");
        println!("✅ Price Impact Calculator: Ready");
        println!("✅ Volatility Detector: Active");
        if self.config.enable_real_time_alerts {
            println!("✅ Real-time Alerts: Enabled");
        }
        println!("{}", "─".repeat(40).green());
        println!("🚀 Phase 2: Real-time Market Data Integration COMPLETE");
        println!("{}", "═".repeat(60).green());
    }

    /// Get system summary
    pub async fn get_system_summary(&self) -> MarketDataSummary {
        let stats = self.system_stats.read().await;
        let alerts = self.active_alerts.read().await;
        
        let mut summary = stats.clone();
        summary.active_alerts = alerts.clone();
        summary
    }

    /// Get active alerts
    pub async fn get_active_alerts(&self) -> Vec<MarketAlert> {
        self.active_alerts.read().await.clone()
    }
}

/// Comprehensive market analysis result
#[derive(Debug, Clone)]
pub struct ComprehensiveAnalysis {
    pub token_pair: (Pubkey, Pubkey),
    pub pools: Vec<crate::core::pool_monitor::PoolState>,
    pub market_depth: crate::core::market_depth::MarketDepth,
    pub price_impact: crate::core::price_impact::PriceImpactAnalysis,
    pub token_a_volatility: Option<crate::core::volatility_detector::VolatilityAnalysis>,
    pub token_b_volatility: Option<crate::core::volatility_detector::VolatilityAnalysis>,
    pub trade_size_recommendation: crate::core::market_depth::TradeSizeRecommendation,
    pub trading_safety_score: f64,
}
