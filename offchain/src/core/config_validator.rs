use anyhow::{Result, anyhow};
use colored::Colorize;
use std::env;
use anchor_client::solana_sdk::signature::Keypair;
use anchor_client::solana_sdk::signer::Signer;
use solana_client::nonblocking::rpc_client::RpcClient;

/// Validates that all required environment variables are properly configured
pub struct ConfigValidator;

impl ConfigValidator {
    /// Validate all required environment variables
    pub fn validate_all() -> Result<()> {
        println!("{}", "🔍 Validating Configuration...".blue().bold());
        
        let mut errors = Vec::new();
        
        // Validate RPC endpoints
        if let Err(e) = Self::validate_rpc_config() {
            errors.push(format!("RPC Configuration: {}", e));
        }
        
        // Validate API keys
        if let Err(e) = Self::validate_api_keys() {
            errors.push(format!("API Keys: {}", e));
        }
        
        // Validate wallet configuration
        if let Err(e) = Self::validate_wallet_config() {
            errors.push(format!("Wallet Configuration: {}", e));
        }
        
        // Validate performance settings
        if let Err(e) = Self::validate_performance_config() {
            errors.push(format!("Performance Configuration: {}", e));
        }
        
        // Validate trading configuration
        if let Err(e) = Self::validate_trading_config() {
            errors.push(format!("Trading Configuration: {}", e));
        }
        
        if errors.is_empty() {
            println!("{}", "✅ All configuration validation passed!".green().bold());
            Self::print_configuration_summary();
            Ok(())
        } else {
            println!("{}", "❌ Configuration validation failed:".red().bold());
            for error in &errors {
                println!("  - {}", error.red());
            }
            Err(anyhow!("Configuration validation failed with {} errors", errors.len()))
        }
    }
    
    /// Validate RPC endpoint configuration
    fn validate_rpc_config() -> Result<()> {
        let rpc_http = env::var("RPC_HTTP")
            .map_err(|_| anyhow!("RPC_HTTP not set"))?;
        
        if !rpc_http.starts_with("http") {
            return Err(anyhow!("RPC_HTTP must be a valid HTTP/HTTPS URL"));
        }
        
        if rpc_http.contains("api-key") {
            println!("  ✅ RPC endpoint configured with API key");
        } else {
            println!("  ⚠️  RPC endpoint configured without API key (may have rate limits)");
        }
        
        // Check websocket endpoint if provided
        if let Ok(ws_endpoint) = env::var("RPC_WEBSOCKET") {
            if !ws_endpoint.starts_with("ws") {
                return Err(anyhow!("RPC_WEBSOCKET must be a valid WebSocket URL"));
            }
            println!("  ✅ WebSocket endpoint configured");
        }
        
        // Check Yellowstone gRPC configuration
        if let Ok(grpc_http) = env::var("YELLOWSTONE_GRPC_HTTP") {
            if !grpc_http.starts_with("http") {
                return Err(anyhow!("YELLOWSTONE_GRPC_HTTP must be a valid HTTP/HTTPS URL"));
            }
            println!("  ✅ Yellowstone gRPC endpoint configured");
        }
        
        Ok(())
    }
    
    /// Validate API keys
    fn validate_api_keys() -> Result<()> {
        // Validate Birdeye API key
        let birdeye_key = env::var("BIRDEYE_API_KEY")
            .map_err(|_| anyhow!("BIRDEYE_API_KEY not set"))?;
        
        if birdeye_key.len() < 10 {
            return Err(anyhow!("BIRDEYE_API_KEY appears to be invalid (too short)"));
        }
        
        println!("  ✅ Birdeye API key configured");
        
        // Check for Yellowstone token
        if let Ok(token) = env::var("YELLOWSTONE_GRPC_TOKEN") {
            if !token.is_empty() && token != "None" {
                println!("  ✅ Yellowstone gRPC token configured");
            }
        }
        
        Ok(())
    }
    
    /// Validate wallet configuration
    fn validate_wallet_config() -> Result<()> {
        let private_key = env::var("PRIVATE_KEY")
            .map_err(|_| anyhow!("PRIVATE_KEY not set"))?;

        if private_key == "YOUR_PRIVATE_KEY_HERE_BASE58_ENCODED" {
            return Err(anyhow!("PRIVATE_KEY is still set to placeholder value"));
        }

        if private_key.len() < 85 {
            return Err(anyhow!("PRIVATE_KEY appears to be invalid (too short for Base58 encoded key)"));
        }

        // Try to parse the private key to validate it
        let keypair = Keypair::from_base58_string(&private_key);
        println!("  ✅ Private key configured and valid");
        println!("  📍 Wallet address: {}", keypair.pubkey());

        Ok(())
    }
    
    /// Validate performance configuration
    fn validate_performance_config() -> Result<()> {
        // Check rate limiting settings
        let rpc_calls = env::var("MAX_CONCURRENT_RPC_CALLS")
            .unwrap_or_else(|_| "15".to_string())
            .parse::<usize>()
            .map_err(|_| anyhow!("MAX_CONCURRENT_RPC_CALLS must be a valid number"))?;
        
        let swaps = env::var("MAX_CONCURRENT_SWAPS")
            .unwrap_or_else(|_| "3".to_string())
            .parse::<usize>()
            .map_err(|_| anyhow!("MAX_CONCURRENT_SWAPS must be a valid number"))?;
        
        if rpc_calls == 0 || swaps == 0 {
            return Err(anyhow!("Concurrent limits must be greater than 0"));
        }
        
        println!("  ✅ Rate limiting: {} RPC calls, {} swaps", rpc_calls, swaps);
        
        // Check circuit breaker settings
        let failure_threshold = env::var("CIRCUIT_BREAKER_FAILURE_THRESHOLD")
            .unwrap_or_else(|_| "5".to_string())
            .parse::<u32>()
            .map_err(|_| anyhow!("CIRCUIT_BREAKER_FAILURE_THRESHOLD must be a valid number"))?;
        
        println!("  ✅ Circuit breaker threshold: {}", failure_threshold);
        
        Ok(())
    }
    
    /// Validate trading configuration
    fn validate_trading_config() -> Result<()> {
        let trade_amount = env::var("DEFAULT_TRADE_AMOUNT")
            .unwrap_or_else(|_| "0.05".to_string())
            .parse::<f64>()
            .map_err(|_| anyhow!("DEFAULT_TRADE_AMOUNT must be a valid number"))?;
        
        if trade_amount <= 0.0 {
            return Err(anyhow!("DEFAULT_TRADE_AMOUNT must be greater than 0"));
        }
        
        let slippage = env::var("MAX_SLIPPAGE_BPS")
            .unwrap_or_else(|_| "100".to_string())
            .parse::<u64>()
            .map_err(|_| anyhow!("MAX_SLIPPAGE_BPS must be a valid number"))?;
        
        if slippage > 10000 {
            return Err(anyhow!("MAX_SLIPPAGE_BPS cannot exceed 10000 (100%)"));
        }
        
        println!("  ✅ Trading: {} SOL per trade, {}% max slippage", trade_amount, slippage as f64 / 100.0);
        
        Ok(())
    }
    
    /// Print a summary of the current configuration
    fn print_configuration_summary() {
        println!("\n{}", "📋 Configuration Summary:".cyan().bold());
        
        // RPC Configuration
        if let Ok(rpc_http) = env::var("RPC_HTTP") {
            let masked_url = Self::mask_api_key(&rpc_http);
            println!("  🌐 RPC Endpoint: {}", masked_url);
        }
        
        // API Keys
        if let Ok(birdeye_key) = env::var("BIRDEYE_API_KEY") {
            let masked_key = Self::mask_api_key(&birdeye_key);
            println!("  🔑 Birdeye API: {}", masked_key);
        }
        
        // Performance Settings
        let rpc_calls = env::var("MAX_CONCURRENT_RPC_CALLS").unwrap_or_else(|_| "15".to_string());
        let swaps = env::var("MAX_CONCURRENT_SWAPS").unwrap_or_else(|_| "3".to_string());
        println!("  ⚡ Rate Limits: {} RPC, {} swaps", rpc_calls, swaps);
        
        // Trading Settings
        let trade_amount = env::var("DEFAULT_TRADE_AMOUNT").unwrap_or_else(|_| "0.05".to_string());
        let slippage = env::var("MAX_SLIPPAGE_BPS").unwrap_or_else(|_| "100".to_string());
        println!("  💰 Trading: {} SOL, {}bps slippage", trade_amount, slippage);
        
        // Emergency Controls
        let emergency_stop = env::var("EMERGENCY_STOP").unwrap_or_else(|_| "false".to_string());
        println!("  🚨 Emergency Stop: {}", emergency_stop);
        
        println!();
    }
    
    /// Mask sensitive information in URLs and API keys
    fn mask_api_key(input: &str) -> String {
        if input.contains("api-key=") {
            let parts: Vec<&str> = input.split("api-key=").collect();
            if parts.len() == 2 {
                let key_part = parts[1];
                let masked_key = if key_part.len() > 8 {
                    format!("{}...{}", &key_part[..4], &key_part[key_part.len()-4..])
                } else {
                    "****".to_string()
                };
                return format!("{}api-key={}", parts[0], masked_key);
            }
        } else if input.len() > 8 {
            // For standalone API keys
            return format!("{}...{}", &input[..4], &input[input.len()-4..]);
        }
        
        "****".to_string()
    }
    
    /// Test API connectivity
    pub async fn test_connectivity() -> Result<()> {
        println!("{}", "🔗 Testing API Connectivity...".blue().bold());
        
        // Test RPC endpoint
        if let Err(e) = Self::test_rpc_connection().await {
            println!("  ❌ RPC connection failed: {}", e.to_string().red());
        } else {
            println!("  ✅ RPC connection successful");
        }
        
        // Test Birdeye API
        if let Err(e) = Self::test_birdeye_connection().await {
            println!("  ❌ Birdeye API connection failed: {}", e.to_string().red());
        } else {
            println!("  ✅ Birdeye API connection successful");
        }
        
        Ok(())
    }
    
    /// Test RPC connection
    async fn test_rpc_connection() -> Result<()> {
        use crate::common::config::create_nonblocking_rpc_client;
        
        let client = create_nonblocking_rpc_client().await?;
        let _version = client.get_version().await
            .map_err(|e| anyhow!("Failed to get RPC version: {}", e))?;
        
        Ok(())
    }
    
    /// Test Birdeye API connection
    async fn test_birdeye_connection() -> Result<()> {
        use crate::core::birdeye_api::BirdeyeClient;
        
        let client = BirdeyeClient::new()?;
        let _sol_price = client.get_sol_price_usd().await
            .map_err(|e| anyhow!("Failed to get SOL price from Birdeye: {}", e))?;
        
        Ok(())
    }

    /// Validate live trading configuration
    pub async fn validate_live_trading_config() -> Result<()> {
        println!("🔍 Validating live trading configuration...");

        // Check if emergency stop is enabled
        let emergency_stop = env::var("EMERGENCY_STOP")
            .unwrap_or_else(|_| "false".to_string())
            .parse::<bool>()
            .unwrap_or(false);

        if emergency_stop {
            return Err(anyhow!("❌ EMERGENCY_STOP is enabled - live trading is disabled"));
        }

        // Check if we're in test mode
        let test_mode = env::var("TEST_MODE")
            .unwrap_or_else(|_| "false".to_string())
            .parse::<bool>()
            .unwrap_or(false);

        if test_mode {
            println!("⚠️  TEST_MODE is enabled - using test configuration");
        }

        // Validate trading parameters
        let default_trade_amount = env::var("DEFAULT_TRADE_AMOUNT")
            .unwrap_or_else(|_| "0.05".to_string())
            .parse::<f64>()
            .map_err(|_| anyhow!("Invalid DEFAULT_TRADE_AMOUNT"))?;

        if default_trade_amount <= 0.0 {
            return Err(anyhow!("DEFAULT_TRADE_AMOUNT must be greater than 0"));
        }

        if default_trade_amount > 1.0 {
            println!("⚠️  WARNING: DEFAULT_TRADE_AMOUNT is quite high ({} SOL)", default_trade_amount);
        }

        // Validate slippage settings
        let max_slippage = env::var("MAX_SLIPPAGE_BPS")
            .unwrap_or_else(|_| "100".to_string())
            .parse::<u64>()
            .map_err(|_| anyhow!("Invalid MAX_SLIPPAGE_BPS"))?;

        if max_slippage > 1000 { // 10%
            println!("⚠️  WARNING: MAX_SLIPPAGE_BPS is quite high ({}bps = {}%)",
                     max_slippage, max_slippage as f64 / 100.0);
        }

        // Validate profit threshold
        let min_profit_threshold = env::var("MIN_PROFIT_THRESHOLD_BPS")
            .unwrap_or_else(|_| "75".to_string())
            .parse::<u64>()
            .map_err(|_| anyhow!("Invalid MIN_PROFIT_THRESHOLD_BPS"))?;

        if min_profit_threshold < 25 { // Less than 0.25%
            println!("⚠️  WARNING: MIN_PROFIT_THRESHOLD_BPS is quite low ({}bps)", min_profit_threshold);
        }

        println!("  ✅ Trading parameters validated");
        println!("    💰 Default trade amount: {} SOL", default_trade_amount);
        println!("    📊 Max slippage: {}bps ({}%)", max_slippage, max_slippage as f64 / 100.0);
        println!("    🎯 Min profit threshold: {}bps ({}%)", min_profit_threshold, min_profit_threshold as f64 / 100.0);

        Ok(())
    }

    /// Validate wallet balance for live trading
    pub async fn validate_wallet_balance() -> Result<()> {
        println!("💰 Checking wallet balance...");

        let private_key = env::var("PRIVATE_KEY")
            .map_err(|_| anyhow!("PRIVATE_KEY not set"))?;

        let keypair = Keypair::from_base58_string(&private_key);

        let rpc_endpoint = env::var("RPC_HTTP")
            .unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string());

        let rpc_client = RpcClient::new(rpc_endpoint);

        match rpc_client.get_account(&keypair.pubkey()).await {
            Ok(account) => {
                let balance_sol = account.lamports as f64 / 1_000_000_000.0;
                println!("  ✅ Wallet balance: {:.6} SOL", balance_sol);

                let default_trade_amount = env::var("DEFAULT_TRADE_AMOUNT")
                    .unwrap_or_else(|_| "0.05".to_string())
                    .parse::<f64>()
                    .unwrap_or(0.05);

                if balance_sol < default_trade_amount * 2.0 {
                    println!("⚠️  WARNING: Wallet balance is low relative to trade amount");
                    println!("    Balance: {:.6} SOL, Trade amount: {} SOL", balance_sol, default_trade_amount);
                }

                if balance_sol < 0.01 {
                    return Err(anyhow!("Insufficient wallet balance for trading (minimum 0.01 SOL required)"));
                }
            },
            Err(e) => {
                return Err(anyhow!("Failed to get wallet balance: {}", e));
            }
        }

        Ok(())
    }
}
