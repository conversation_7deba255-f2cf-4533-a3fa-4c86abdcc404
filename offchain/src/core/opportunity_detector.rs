//! Arbitrage Opportunity Detection System
//! 
//! This module detects arbitrage opportunities across different DEXes and strategies,
//! then feeds them into the priority queue for execution.

use std::collections::HashMap;
use std::time::{Duration, Instant};
use std::sync::Arc;
use tokio::sync::RwLock;
use anyhow::{Result, anyhow};
use solana_sdk::pubkey::Pubkey;
use uuid::Uuid;

use crate::core::priority_queue::{ArbitrageOpportunity, ArbitrageStrategy, OpportunityQueue};
use crate::core::token::{TokenModel, TokenPrice};
use crate::dex::dex_registry::DEXRegistry;

/// Configuration for opportunity detection
#[derive(Clone, Debug)]
pub struct OpportunityDetectorConfig {
    pub min_profit_threshold_pct: f64,
    pub max_route_complexity: u8,
    pub opportunity_ttl_seconds: u64,
    pub min_liquidity_sol: f64,
    pub max_slippage_pct: f64,
    pub gas_estimation_enabled: bool,
    pub enable_multi_dex: bool,
    pub enable_two_hop: bool,
    pub enable_triangle: bool,
}

impl Default for OpportunityDetectorConfig {
    fn default() -> Self {
        Self {
            min_profit_threshold_pct: 0.5,
            max_route_complexity: 3,
            opportunity_ttl_seconds: 30,
            min_liquidity_sol: 0.1,
            max_slippage_pct: 1.0,
            gas_estimation_enabled: true,
            enable_multi_dex: true,
            enable_two_hop: true,
            enable_triangle: false, // Disabled by default due to complexity
        }
    }
}

/// Main opportunity detection engine
pub struct OpportunityDetector {
    config: OpportunityDetectorConfig,
    token_model: Arc<RwLock<TokenModel>>,
    dex_registry: Arc<DEXRegistry>,
    opportunity_queue: Arc<OpportunityQueue>,
    gas_estimator: Arc<GasEstimator>,
}

impl OpportunityDetector {
    /// Create a new opportunity detector
    pub fn new(
        config: OpportunityDetectorConfig,
        token_model: Arc<RwLock<TokenModel>>,
        dex_registry: Arc<DEXRegistry>,
        opportunity_queue: Arc<OpportunityQueue>,
    ) -> Self {
        Self {
            config,
            token_model,
            dex_registry,
            opportunity_queue,
            gas_estimator: Arc::new(GasEstimator::new()),
        }
    }

    /// Scan for all types of arbitrage opportunities
    pub async fn scan_for_opportunities(&self) -> Result<usize> {
        let mut total_opportunities = 0;

        // Get all tokens with price data
        let token_model = self.token_model.read().await;
        let all_tokens = token_model.get_all_tokens_with_prices();
        drop(token_model);

        for (mint_str, token_prices) in all_tokens {
            let mint = Pubkey::try_from(mint_str.as_str())
                .map_err(|_| anyhow!("Invalid mint pubkey: {}", mint_str))?;

            // Multi-DEX arbitrage opportunities
            if self.config.enable_multi_dex {
                let multi_dex_opps = self.detect_multi_dex_opportunities(&mint, &token_prices).await?;
                for opp in multi_dex_opps {
                    self.opportunity_queue.add_opportunity(opp).await?;
                    total_opportunities += 1;
                }
            }

            // Two-hop arbitrage opportunities
            if self.config.enable_two_hop {
                let two_hop_opps = self.detect_two_hop_opportunities(&mint, &token_prices).await?;
                for opp in two_hop_opps {
                    self.opportunity_queue.add_opportunity(opp).await?;
                    total_opportunities += 1;
                }
            }

            // Triangle arbitrage opportunities
            if self.config.enable_triangle {
                let triangle_opps = self.detect_triangle_opportunities(&mint, &token_prices).await?;
                for opp in triangle_opps {
                    self.opportunity_queue.add_opportunity(opp).await?;
                    total_opportunities += 1;
                }
            }
        }

        Ok(total_opportunities)
    }

    /// Detect multi-DEX arbitrage opportunities (direct arbitrage between two DEXes)
    async fn detect_multi_dex_opportunities(
        &self,
        mint: &Pubkey,
        token_prices: &HashMap<String, TokenPrice>,
    ) -> Result<Vec<ArbitrageOpportunity>> {
        let mut opportunities = Vec::new();
        let dexes: Vec<&String> = token_prices.keys().collect();

        for i in 0..dexes.len() {
            for j in i + 1..dexes.len() {
                let dex1 = dexes[i];
                let dex2 = dexes[j];

                if let (Some(price1), Some(price2)) = (token_prices.get(dex1), token_prices.get(dex2)) {
                    let price_diff_pct = ((price1.price - price2.price).abs() / price2.price.min(price1.price)) * 100.0;

                    if price_diff_pct >= self.config.min_profit_threshold_pct {
                        // Determine buy and sell DEXes
                        let (buy_dex, sell_dex, buy_price, sell_price) = if price1.price < price2.price {
                            (dex1.clone(), dex2.clone(), price1.price, price2.price)
                        } else {
                            (dex2.clone(), dex1.clone(), price2.price, price1.price)
                        };

                        // Calculate trade amount based on available liquidity
                        let trade_amount = self.calculate_optimal_trade_amount(
                            price1.liquidity.min(price2.liquidity),
                            buy_price,
                        );

                        if trade_amount == 0 {
                            continue; // Skip if no viable trade amount
                        }

                        // Estimate gas cost
                        let estimated_gas_cost = if self.config.gas_estimation_enabled {
                            self.gas_estimator.estimate_multi_dex_gas(&buy_dex, &sell_dex).await?
                        } else {
                            200_000 // Default estimate
                        };

                        // Calculate profit
                        let estimated_profit_sol = ((sell_price - buy_price) * trade_amount as f64) / 1_000_000_000.0;
                        let estimated_profit_pct = price_diff_pct;

                        let opportunity = ArbitrageOpportunity {
                            id: Uuid::new_v4().to_string(),
                            token_mint: *mint,
                            strategy_type: ArbitrageStrategy::MultiDex,
                            buy_dex: buy_dex.to_string(),
                            sell_dex: sell_dex.to_string(),
                            buy_price,
                            sell_price,
                            price_difference_pct: price_diff_pct,
                            estimated_profit_sol,
                            estimated_profit_pct,
                            liquidity_available: price1.liquidity.min(price2.liquidity),
                            detected_at: Instant::now(),
                            expires_at: Instant::now() + Duration::from_secs(self.config.opportunity_ttl_seconds),
                            trade_amount,
                            estimated_gas_cost,
                            route_complexity: 1,
                        };

                        opportunities.push(opportunity);
                    }
                }
            }
        }

        Ok(opportunities)
    }

    /// Detect two-hop arbitrage opportunities
    async fn detect_two_hop_opportunities(
        &self,
        mint: &Pubkey,
        _token_prices: &HashMap<String, TokenPrice>,
    ) -> Result<Vec<ArbitrageOpportunity>> {
        let mut opportunities = Vec::new();

        // Get common intermediate tokens (SOL, USDC, etc.)
        let intermediate_tokens = self.get_common_intermediate_tokens();

        for intermediate_mint in intermediate_tokens {
            // Check if we can find a profitable route: mint -> intermediate -> mint
            if let Some(opportunity) = self.find_two_hop_route(mint, &intermediate_mint).await? {
                opportunities.push(opportunity);
            }
        }

        Ok(opportunities)
    }

    /// Detect triangle arbitrage opportunities
    async fn detect_triangle_opportunities(
        &self,
        mint: &Pubkey,
        _token_prices: &HashMap<String, TokenPrice>,
    ) -> Result<Vec<ArbitrageOpportunity>> {
        let mut opportunities = Vec::new();

        // Get pairs of intermediate tokens for triangle routes
        let intermediate_tokens = self.get_common_intermediate_tokens();

        for i in 0..intermediate_tokens.len() {
            for j in i + 1..intermediate_tokens.len() {
                let token_b = intermediate_tokens[i];
                let token_c = intermediate_tokens[j];

                // Check route: mint -> token_b -> token_c -> mint
                if let Some(opportunity) = self.find_triangle_route(mint, &token_b, &token_c).await? {
                    opportunities.push(opportunity);
                }
            }
        }

        Ok(opportunities)
    }

    /// Calculate optimal trade amount based on liquidity and price
    fn calculate_optimal_trade_amount(&self, available_liquidity: u64, price: f64) -> u64 {
        let min_liquidity_lamports = (self.config.min_liquidity_sol * 1_000_000_000.0) as u64;
        
        if available_liquidity < min_liquidity_lamports {
            return 0;
        }

        // Use a conservative percentage of available liquidity to minimize slippage
        let conservative_amount = (available_liquidity as f64 * 0.1) as u64; // 10% of liquidity
        let max_trade_value_lamports = (1.0 * 1_000_000_000.0) as u64; // Max 1 SOL trade value
        let max_trade_amount = (max_trade_value_lamports as f64 / price) as u64;

        conservative_amount.min(max_trade_amount)
    }

    /// Get list of common intermediate tokens for multi-hop arbitrage
    fn get_common_intermediate_tokens(&self) -> Vec<Pubkey> {
        vec![
            // SOL
            Pubkey::try_from("So11111111111111111111111111111111111111112").unwrap(),
            // USDC
            Pubkey::try_from("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v").unwrap(),
            // USDT
            Pubkey::try_from("Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB").unwrap(),
        ]
    }

    /// Find a profitable two-hop route
    async fn find_two_hop_route(
        &self,
        start_mint: &Pubkey,
        intermediate_mint: &Pubkey,
    ) -> Result<Option<ArbitrageOpportunity>> {
        // This is a simplified implementation
        // In practice, you would:
        // 1. Get prices for start_mint -> intermediate_mint on all DEXes
        // 2. Get prices for intermediate_mint -> start_mint on all DEXes
        // 3. Find the most profitable combination
        // 4. Calculate gas costs and slippage
        
        // For now, return None as this requires more complex price routing
        Ok(None)
    }

    /// Find a profitable triangle route
    async fn find_triangle_route(
        &self,
        start_mint: &Pubkey,
        token_b: &Pubkey,
        token_c: &Pubkey,
    ) -> Result<Option<ArbitrageOpportunity>> {
        // This is a simplified implementation
        // In practice, you would:
        // 1. Get prices for start_mint -> token_b
        // 2. Get prices for token_b -> token_c
        // 3. Get prices for token_c -> start_mint
        // 4. Calculate if the complete route is profitable
        // 5. Account for gas costs and slippage
        
        // For now, return None as this requires complex multi-hop routing
        Ok(None)
    }
}

/// Gas cost estimator for different arbitrage strategies
pub struct GasEstimator {
    base_gas_costs: HashMap<String, u64>,
}

impl GasEstimator {
    pub fn new() -> Self {
        let mut base_gas_costs = HashMap::new();
        
        // Base gas costs for different DEX operations (in compute units)
        base_gas_costs.insert("raydium".to_string(), 150_000);
        base_gas_costs.insert("orca".to_string(), 120_000);
        base_gas_costs.insert("meteora".to_string(), 180_000);
        base_gas_costs.insert("whirlpool".to_string(), 140_000);
        base_gas_costs.insert("pumpswap".to_string(), 100_000);

        Self { base_gas_costs }
    }

    /// Estimate gas cost for multi-DEX arbitrage
    pub async fn estimate_multi_dex_gas(&self, buy_dex: &str, sell_dex: &str) -> Result<u64> {
        let buy_cost = self.base_gas_costs.get(buy_dex).unwrap_or(&150_000);
        let sell_cost = self.base_gas_costs.get(sell_dex).unwrap_or(&150_000);
        
        // Add overhead for transaction coordination
        let total_cost = buy_cost + sell_cost + 50_000;
        
        Ok(total_cost)
    }

    /// Estimate gas cost for two-hop arbitrage
    pub async fn estimate_two_hop_gas(&self, dex1: &str, dex2: &str) -> Result<u64> {
        let cost1 = self.base_gas_costs.get(dex1).unwrap_or(&150_000);
        let cost2 = self.base_gas_costs.get(dex2).unwrap_or(&150_000);
        
        // Two-hop requires more coordination
        let total_cost = cost1 + cost2 + 100_000;
        
        Ok(total_cost)
    }

    /// Estimate gas cost for triangle arbitrage
    pub async fn estimate_triangle_gas(&self, dex1: &str, dex2: &str, dex3: &str) -> Result<u64> {
        let cost1 = self.base_gas_costs.get(dex1).unwrap_or(&150_000);
        let cost2 = self.base_gas_costs.get(dex2).unwrap_or(&150_000);
        let cost3 = self.base_gas_costs.get(dex3).unwrap_or(&150_000);
        
        // Triangle arbitrage has highest coordination overhead
        let total_cost = cost1 + cost2 + cost3 + 150_000;
        
        Ok(total_cost)
    }
}
